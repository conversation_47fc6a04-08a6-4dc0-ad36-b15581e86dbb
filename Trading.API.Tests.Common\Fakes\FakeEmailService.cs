﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.Services.Interfaces;

namespace Trading.API.Tests.Common.Fakes
{
  public class FakeEmailService : IEmailService
  {
    public Task<bool> SendEmail(SendEmailDTO dto, CancellationToken cancellationToken)
    {
      return Task.FromResult(true);
    }

    public Task<bool> TestSendEmail(CancellationToken cancellationToken)
    {
      throw new NotImplementedException();
    }

    public Task<bool> ValidateEmail(string emailAddress)
    {
      throw new NotImplementedException();
    }
  }
}
