using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Quartz;
using System.IO;
using System.Reflection;
using Trading.API.Common.APIKeyAuth;
using Trading.API.Remarq.QuartzJobs;

namespace Trading.API
{
  public class Program
  {
    public static void Main(string[] args)
    {
      CreateHostBuilder(args).Build().Run();
    }

    public static IHostBuilder CreateHostBuilder(string[] args) =>

        Host.CreateDefaultBuilder(args)

            .ConfigureAppConfiguration((hostContext, config) =>
            {
              config.SetBasePath(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location));
              config.AddEnvironmentVariables();
            })
            .ConfigureWebHostDefaults(webBuilder =>
            {
              webBuilder.UseStartup<Startup>();

              // Add the following line:
              webBuilder.UseSentry(o =>
              {
                o.Dsn = "https://<EMAIL>/4507901251682384";
                // When configuring for the first time, to see what the SDK is doing:
                o.Debug = false;
                // Set TracesSampleRate to 1.0 to capture 100%
                // of transactions for tracing.
                // We recommend adjusting this value in production
                o.TracesSampleRate = 0.1;
              });
            })
            .ConfigureServices((context, services) =>
            {
              //              services.AddHostedService<ScanWorkerService>();
              //              services.AddHostedService<AuctionWorkerService>();

              var env = context.HostingEnvironment;

              // Add API key authentication
              services.AddApiKeyAuthentication();

              // Don't run Quartz in Dev !
              if (!env.IsDevelopment())
              {
                // Quartz configuration
                services.AddQuartz(q =>
                {
                  // DAILY ALERTS
                  var dailyJobKey = new JobKey("DailySummaryAlertsJob");
                  q.AddJob<DailySummaryAlertsJob>(opts => opts.WithIdentity(dailyJobKey));
                  q.AddTrigger(opts => opts
                      .ForJob(dailyJobKey)
                      .WithIdentity("DailySummaryAlertsJob-trigger")
                      .WithCronSchedule("0 20 18 ? * *")); // 18:20 every day

                  // INSTANT ALERTS
                  var instantJobKey = new JobKey("InstantAdvertAlertsJob");
                  q.AddJob<InstantAdvertAlertsJob>(opts => opts.WithIdentity(instantJobKey));
                  q.AddTrigger(opts => opts
                      .ForJob(instantJobKey)
                      .WithIdentity("InstantAdvertAlertsJob-trigger")
                      .WithCronSchedule("0 */20 * ? * *")); // Every 20 minutes

                  // EMAIL ANALYTICS
                  var emailAnalyticsJobKey = new JobKey("EmailAnalyticsJob");
                  q.AddJob<EmailAnalyticsJob>(opts => opts.WithIdentity(emailAnalyticsJobKey));
                  q.AddTrigger(opts => opts
                      .ForJob(emailAnalyticsJobKey)
                      .WithIdentity("EmailAnalyticsJob-trigger")
                      .WithCronSchedule("0 0 0 * * ?")); // Every day at midnight
                });
   

                // Quartz hosted service (automatically starts Quartz scheduler)
                services.AddQuartzHostedService(q => q.WaitForJobsToComplete = true);
              }
            });
  }
}
