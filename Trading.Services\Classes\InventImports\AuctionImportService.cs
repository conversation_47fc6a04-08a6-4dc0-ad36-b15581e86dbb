using Microsoft.AspNetCore.JsonPatch;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Imports.Invent;
using Trading.API.Data.Enums;
using Trading.API.Data.Enums.Imports;
using Trading.API.Data.Models;
using Trading.API.Data.Models.InventData;
using Trading.Services.ExternalDTO;
using Trading.Services.Helpers;
using Trading.Services.Interfaces;
using Trading.Services.Interfaces.InventImports;

namespace Trading.Services.Classes.InventImports;


/// <summary>
/// Service for importing auction data and creating vehicles from auction lots
/// </summary>
public class AuctionImportService : IAuctionImportService
{
  private readonly TradingContext _tradingContext;
  private readonly IAuctionApiClient _auctionApiClient;
  private readonly IAuctionVehicleMappingService _mappingService;
  private readonly ILogger<AuctionImportService> _logger;
  private readonly IAdvertService _advertService;
  private readonly IVehicleService _vehicleService;
  private readonly IUserService _userService;
  private readonly IVehicleMediaService _mediaService;
  private readonly IVehicleCheckService _vehicleCheckService;
  private readonly IInventInspectionPDFExtractorInterface _inspectionPDFExtractor;
  private readonly ISaleService _saleService;

  // Component names for searches
  private const string COMPONENT_AVAILABLE_LOTS = "AvailableLots";
  private const string COMPONENT_IMPORTED_VEHICLES = "ImportedVehicles";
  private const string COMPONENT_SOLD_VEHICLES = "SoldVehicles";
  private const string COMPONENT_REMOVED_VEHICLES = "RemovedVehicles";

  public AuctionImportService(
      TradingContext tradingContext,
      IAuctionApiClient auctionApiClient,
      IAuctionVehicleMappingService mappingService,
      ILogger<AuctionImportService> logger,
      IAdvertService advertService, 
      IVehicleService vehicleService,
      IUserService userService,
      IVehicleMediaService mediaService,
      IVehicleCheckService vehicleCheckService,
      IInventInspectionPDFExtractorInterface inspectionPDFExtractor, 
      ISaleService saleService)
  {
    _tradingContext = tradingContext ?? throw new ArgumentNullException(nameof(tradingContext));
    _auctionApiClient = auctionApiClient ?? throw new ArgumentNullException(nameof(auctionApiClient));
    _mappingService = mappingService ?? throw new ArgumentNullException(nameof(mappingService));
    _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    _advertService = advertService;
    _vehicleService = vehicleService;
    _userService = userService;
    _mediaService = mediaService;
    _vehicleCheckService = vehicleCheckService;
    _inspectionPDFExtractor = inspectionPDFExtractor;
    _saleService = saleService;
  }

  private class ProcessingResult
  {
    public int SuccessCount { get; set; } = 0;
    public List<string> ErrorMessages { get; set; } = new List<string>();
  }

  /// <summary>
  /// Fetches all auctions and their lots from the API and stores them in the database
  /// </summary>
  public async Task<int> ImportAuctionsAsync(Guid inventUserId, CancellationToken cancellationToken = default)
  {
    try
    {
      // Get the invent user info for specified id
      var inventUser = await _tradingContext.InventUsers
          .AsNoTracking()
          .FirstOrDefaultAsync(x => x.Id == inventUserId, cancellationToken);

      if (inventUser == null)
      {
        _logger.LogWarning($"No Invent user found for invent user ID {inventUserId}");
        return 0;
      }

      var auctionId = inventUser.AuctionId;
      _logger.LogInformation($"Starting import of auction {auctionId} and lots");

      // Fetch auction data from API
      var auctionWithLots = await _auctionApiClient.FetchAuctionWithLotsAsync(auctionId, cancellationToken);
      auctionWithLots.InventUserId = inventUser.Id;

      if (auctionWithLots == null)
      {
        _logger.LogWarning("No auction found to import");
        return 0;
      }

      if (auctionWithLots.Lots == null)
      {
        _logger.LogWarning("No auction lots found to import");
        return 0;
      }

      var lots = auctionWithLots.Lots.OrderBy(x => x.LotNumber).ToList();
      if (lots.Count == 0)
      {
        _logger.LogInformation($"No lots found for auction {auctionId}");
        return 0;
      }

      // Get existing VINs and VRMs from Vehicles table only (to avoid duplicates)
      // and existing auction lots (to update prices)
      var (existingVehicleIdentifiers, existingAuctionLots) = await GetExistingIdentifiersAndLotsAsync(lots, cancellationToken);

      // Filter out lots that already exist as vehicles, but keep lots that exist in auction lots for price updates
      var availableLots = lots.Where(lot =>
      {
        // If lot has a VIN and it exists in Vehicles table, filter it out
        if (!string.IsNullOrEmpty(lot.Vin) && existingVehicleIdentifiers.Vins.Contains(lot.Vin))
          return false;

        // If lot has a VRM and it exists in Vehicles table, filter it out
        if (!string.IsNullOrEmpty(lot.Vrm) && existingVehicleIdentifiers.Vrms.Contains(lot.Vrm))
          return false;

        // Otherwise, keep the lot (including existing auction lots for price updates)
        return true;
      }).ToList();

      if (availableLots.Count == 0)
      {
        _logger.LogInformation($"No available lots to import for auction {auctionId}");
        return 0;
      }

      // Import within a transaction to ensure data consistency
      using var transaction = await _tradingContext.Database.BeginTransactionAsync(cancellationToken);
      try
      {
        var importedCount = await ImportAuctionAndLotsAsync(auctionWithLots, availableLots, existingAuctionLots, cancellationToken);
        await transaction.CommitAsync(cancellationToken);

        _logger.LogInformation($"Found {lots.Count} total lots, imported {importedCount} available lots from auction {auctionId}");
        return importedCount;
      }
      catch
      {
        await transaction.RollbackAsync(cancellationToken);
        throw;
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error occurred while importing auctions and lots");
      throw;
    }
  }

  private async Task<((HashSet<string> Vins, HashSet<string> Vrms) VehicleIdentifiers, Dictionary<string, InventAuctionLot> ExistingAuctionLots)> GetExistingIdentifiersAndLotsAsync(
      List<InventAuctionLot> lots,
      CancellationToken cancellationToken)
  {
    // Extract VINs and VRMs from the lots we're trying to import
    var lotVins = lots.Where(l => !string.IsNullOrEmpty(l.Vin)).Select(l => l.Vin).ToHashSet();
    var lotVrms = lots.Where(l => !string.IsNullOrEmpty(l.Vrm)).Select(l => l.Vrm).ToHashSet();

    // Get existing vehicle identifiers (to prevent duplicates)
    var existingVehicleVins = new HashSet<string>();
    var existingVehicleVrms = new HashSet<string>();

    if (lotVins.Count > 0)
    {
      var vehicleVins = await _tradingContext.Vehicles
          .AsNoTracking()
          .Where(v => v.StatusId == (uint)StatusEnum.Active && lotVins.Contains(v.Vin))
          .Select(v => v.Vin)
          .ToListAsync(cancellationToken);

      existingVehicleVins.UnionWith(vehicleVins);
    }

    if (lotVrms.Count > 0)
    {
      var vehicleVrms = await _tradingContext.Vehicles
          .AsNoTracking()
          .Where(v => v.StatusId == (uint)StatusEnum.Active && lotVrms.Contains(v.Vrm))
          .Select(v => v.Vrm)
          .ToListAsync(cancellationToken);

      existingVehicleVrms.UnionWith(vehicleVrms);
    }

    // Get existing auction lots (for price updates)
    var existingAuctionLots = new Dictionary<string, InventAuctionLot>();

    if (lotVins.Count > 0)
    {
      var auctionLotsByVin = await _tradingContext.InventAuctionLots
          .AsNoTracking()
          .Where(lot => !string.IsNullOrEmpty(lot.Vin) && lotVins.Contains(lot.Vin))
          .ToListAsync(cancellationToken);

      foreach (var lot in auctionLotsByVin)
      {
        existingAuctionLots[lot.Vin] = lot;
      }
    }

    if (lotVrms.Count > 0)
    {
      var auctionLotsByVrm = await _tradingContext.InventAuctionLots
          .AsNoTracking()
          .Where(lot => !string.IsNullOrEmpty(lot.Vrm) && lotVrms.Contains(lot.Vrm))
          .ToListAsync(cancellationToken);

      foreach (var lot in auctionLotsByVrm)
      {
        // Use VRM as key if VIN is not available or already used
        if (string.IsNullOrEmpty(lot.Vin) || !existingAuctionLots.ContainsKey(lot.Vin))
        {
          existingAuctionLots[lot.Vrm] = lot;
        }
      }
    }

    return ((existingVehicleVins, existingVehicleVrms), existingAuctionLots);
  }

  private async Task<int> ImportAuctionAndLotsAsync(
      InventAuction auctionWithLots,
      List<InventAuctionLot> availableLots,
      Dictionary<string, InventAuctionLot> existingAuctionLots,
      CancellationToken cancellationToken)
  {
    // Check if auction already exists
    var existingAuction = await _tradingContext.InventAuctions
        .FirstOrDefaultAsync(a => a.AuctionId == auctionWithLots.AuctionId, cancellationToken);

    Guid auctionEntityId;
    if (existingAuction == null)
    {
      await _tradingContext.InventAuctions.AddAsync(auctionWithLots, cancellationToken);
      await _tradingContext.SaveChangesAsync(cancellationToken);
      auctionEntityId = auctionWithLots.Id;
    }
    else
    {
      auctionEntityId = existingAuction.Id;
    }

    // Separate new lots from existing lots that need price updates
    var newLots = new List<InventAuctionLot>();
    var updatedCount = 0;

    foreach (var lot in availableLots)
    {
      lot.AuctionId = auctionEntityId;

      // Check if this lot already exists (by VIN or VRM)
      InventAuctionLot existingLot = null;
      if (!string.IsNullOrEmpty(lot.Vin) && existingAuctionLots.ContainsKey(lot.Vin))
      {
        existingLot = existingAuctionLots[lot.Vin];
      }
      else if (!string.IsNullOrEmpty(lot.Vrm) && existingAuctionLots.ContainsKey(lot.Vrm))
      {
        existingLot = existingAuctionLots[lot.Vrm];
      }

      if (existingLot != null)
      {
        // Update price fields for existing lot
        existingLot.ReservePrice = lot.ReservePrice;
        existingLot.CurrentBid = lot.CurrentBid;
        existingLot.Updated = DateTime.Now;

        _tradingContext.InventAuctionLots.Update(existingLot);
        updatedCount++;

        _logger.LogInformation($"Updated price for existing auction lot {existingLot.Id} (VIN: {existingLot.Vin}, VRM: {existingLot.Vrm}) - Reserve: {lot.ReservePrice}, Current Bid: {lot.CurrentBid}");
      }
      else
      {
        // This is a new lot
        newLots.Add(lot);
      }
    }

    // Batch insert new lots
    if (newLots.Count > 0)
    {
      await _tradingContext.InventAuctionLots.AddRangeAsync(newLots, cancellationToken);
    }

    await _tradingContext.SaveChangesAsync(cancellationToken);

    _logger.LogInformation($"Imported {newLots.Count} new lots and updated prices for {updatedCount} existing lots");
    return newLots.Count + updatedCount;
  }

  /// <summary>
  /// Searches for auctions based on provided filters
  /// </summary>
  /// <param name="searchDTO">Search parameters</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>Search results with auctions</returns>
  public async Task<SearchResultDTO<InventAuctionDTO>> SearchAuctionsAsync(InventAuctionSearchDTO searchDTO, CancellationToken cancellationToken = default)
  {
    try
    {
      // Initialize search DTO if null
      searchDTO ??= new InventAuctionSearchDTO();

      // Create base query
      var auctionsQuery = _tradingContext.InventAuctions.AsQueryable();

      // Apply activity status filter if provided
      if (searchDTO.Filters.ActivityStatus.HasValue)
      {
        auctionsQuery = auctionsQuery.Where(a => a.ActivityStatus == searchDTO.Filters.ActivityStatus.Value);
      }

      // Apply auction type filter if provided
      if (!string.IsNullOrEmpty(searchDTO.Filters.AuctionType))
      {
        auctionsQuery = auctionsQuery.Where(a => a.AuctionTypeTitle.ToLower() == searchDTO.Filters.AuctionType.ToLower());
      }

      // Apply auction location filter if provided
      if (!string.IsNullOrEmpty(searchDTO.Filters.AuctionLocation))
      {
        auctionsQuery = auctionsQuery.Where(a => a.AuctionLocationTitle.ToLower() == searchDTO.Filters.AuctionLocation.ToLower());
      }

      // Apply date filters if provided
      if (searchDTO.Filters.DateFrom.HasValue)
      {
        auctionsQuery = auctionsQuery.Where(a => a.DateTime >= searchDTO.Filters.DateFrom.Value);
      }

      if (searchDTO.Filters.DateTo.HasValue)
      {
        auctionsQuery = auctionsQuery.Where(a => a.DateTime <= searchDTO.Filters.DateTo.Value);
      }

      // Apply search term filter if provided via base filter
      if (!string.IsNullOrEmpty(searchDTO.Filters.SearchTerm))
      {
        var searchTerm = searchDTO.Filters.SearchTerm.ToLower();
        auctionsQuery = auctionsQuery.Where(a =>
            EF.Functions.Like(a.Title.ToLower(), $"%{searchTerm}%") ||
            EF.Functions.Like(a.AuctionTypeTitle.ToLower(), $"%{searchTerm}%") ||
            EF.Functions.Like(a.AuctionLocationTitle.ToLower(), $"%{searchTerm}%"));
      }

      // Apply status ID filter if provided
      if (searchDTO.Filters.StatusId.HasValue)
      {
        // This assumes StatusId maps to ActivityStatus in some way
        auctionsQuery = auctionsQuery.Where(a => a.ActivityStatus == (int)searchDTO.Filters.StatusId.Value);
      }

      // Get total count
      var totalItems = await auctionsQuery.CountAsync(cancellationToken);

      // Return count only if requested
      if (searchDTO.CountOnly)
      {
        return new SearchResultDTO<InventAuctionDTO>
        {
          Results = Enumerable.Empty<InventAuctionDTO>(),
          TotalItems = totalItems
        };
      }

      // Apply sorting if specified
      if (searchDTO.Order != null && searchDTO.Order.Any())
      {
        var firstOrderBy = searchDTO.Order.First();
        auctionsQuery = ApplySortingToAuctions(auctionsQuery, firstOrderBy);
      }
      else
      {
        // Default sorting by DateTime descending
        auctionsQuery = auctionsQuery.OrderByDescending(a => a.DateTime);
      }

      // Apply pagination
      if (searchDTO.Offset.HasValue && searchDTO.Limit.HasValue)
      {
        auctionsQuery = auctionsQuery
            .Skip(searchDTO.Offset.Value)
            .Take(searchDTO.Limit.Value);
      }

      // Execute the query
      var auctions = await auctionsQuery.ToListAsync(cancellationToken);

      // Get lot counts for each auction
      var auctionIds = auctions.Select(a => a.Id).ToList();
      var lotCounts = await _tradingContext.InventAuctionLots
          .Where(l => auctionIds.Contains(l.AuctionId))
          .GroupBy(l => l.AuctionId)
          .Select(g => new { AuctionId = g.Key, Count = g.Count() })
          .ToListAsync(cancellationToken);

      // Map to DTOs
      var result = auctions.Select(auction => new InventAuctionDTO
      {
        Id = auction.Id,
        Title = auction.Title,
        DateTime = auction.DateTime,
        EndDateTime = auction.EndDateTime,
        ActivityStatus = auction.ActivityStatus,
        AuctionTypeTitle = auction.AuctionTypeTitle,
        AuctionLocationId = auction.AuctionLocationId,
        AuctionLocationTitle = auction.AuctionLocationTitle,
        LotsCount = lotCounts.FirstOrDefault(c => c.AuctionId == auction.Id)?.Count ?? 0
      }).ToList();

      return new SearchResultDTO<InventAuctionDTO>
      {
        Results = result,
        TotalItems = totalItems
      };
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error searching auctions");
      throw;
    }
  }

  /// <summary>
  /// Applies sorting to auction query
  /// </summary>
  private IQueryable<InventAuction> ApplySortingToAuctions(
      IQueryable<InventAuction> query, OrderByDTO orderBy)
  {
    if (orderBy == null)
      return query;

    var isDescending = orderBy.Descending ?? false;

    switch (orderBy.Column?.ToLower())
    {
      case "title":
        return isDescending
            ? query.OrderByDescending(a => a.Title)
            : query.OrderBy(a => a.Title);
      case "datetime":
      case "date":
        return isDescending
            ? query.OrderByDescending(a => a.DateTime)
            : query.OrderBy(a => a.DateTime);
      case "status":
      case "activitystatus":
        return isDescending
            ? query.OrderByDescending(a => a.ActivityStatus)
            : query.OrderBy(a => a.ActivityStatus);
      case "location":
      case "auctionlocationtitle":
        return isDescending
            ? query.OrderByDescending(a => a.AuctionLocationTitle)
            : query.OrderBy(a => a.AuctionLocationTitle);
      case "type":
      case "auctiontypetitle":
        return isDescending
            ? query.OrderByDescending(a => a.AuctionTypeTitle)
            : query.OrderBy(a => a.AuctionTypeTitle);
      case "lotscount":
        // For sorting by lots count, we'll need a more complex approach
        // For now, default to date sorting
        return isDescending
            ? query.OrderByDescending(a => a.DateTime)
            : query.OrderBy(a => a.DateTime);
      default:
        return isDescending
            ? query.OrderByDescending(a => a.DateTime)
            : query.OrderBy(a => a.DateTime);
    }
  }

  /// <summary>
  /// Consolidated search method for getting auction lots with various filters
  /// </summary>
  /// <param name="searchDTO">Search parameters</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>Search results with auction lots</returns>
  public async Task<SearchResultDTO<InventAuctionLotDTO>> SearchAsync(InventSearchDTO searchDTO, CancellationToken cancellationToken = default)
  {
    try
    {
      // Initialize search DTO if null
      searchDTO ??= new InventSearchDTO();

      if (!searchDTO.Filters.InventUserId.HasValue && !_userService.IsAdminOrGreater())
      {
        _logger.LogWarning("InventUserId filter is required for non-admin users");
        return new SearchResultDTO<InventAuctionLotDTO>
        {
          Results = Enumerable.Empty<InventAuctionLotDTO>(),
          TotalItems = 0
        };
      }

      _logger.LogInformation($"Starting search with component: {searchDTO.Component}");

      // Select the appropriate query based on the component
      switch (searchDTO.Component)
      {
        case COMPONENT_AVAILABLE_LOTS:
          return await SearchAvailableLotsAsync(searchDTO, cancellationToken);
        case COMPONENT_IMPORTED_VEHICLES:
          return await SearchImportedLotsAsync(searchDTO, cancellationToken);
        case COMPONENT_SOLD_VEHICLES:
          return await SearchSoldLotsAsync(searchDTO, cancellationToken);
        case COMPONENT_REMOVED_VEHICLES:
          return await SearchRemovedLotsAsync(searchDTO, cancellationToken);
        default:
          _logger.LogWarning($"Unknown component: {searchDTO.Component}");
          return new SearchResultDTO<InventAuctionLotDTO>
          {
            Results = Enumerable.Empty<InventAuctionLotDTO>(),
            TotalItems = 0
          };
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, $"Error performing search with component: {searchDTO.Component}");
      throw;
    }
  }

  /// <summary>
  /// Searches for removed lots
  /// </summary>
  private async Task<SearchResultDTO<InventAuctionLotDTO>> SearchRemovedLotsAsync(
      InventSearchDTO searchDTO, CancellationToken cancellationToken)
  {
    // Get lots that are flagged as withdrawn (status 5)
    var lotsQuery = _tradingContext.InventAuctionLots
        .Include(x => x.Images)
        .Where(lot => lot.Status == (int)InventAuctionLotStatus.Withdrawn)
        .Include(lot => lot.Auction)
        .AsQueryable();

    // Apply AuctionId filter if provided
    if (searchDTO.Filters.InventUserId.HasValue)
    {
      lotsQuery = lotsQuery.Where(lot => lot.Auction.InventUserId == searchDTO.Filters.InventUserId.Value);
    }

    // Apply date filters if provided
    if (searchDTO.Filters.DateFrom.HasValue)
    {
      lotsQuery = lotsQuery.Where(lot => lot.Updated >= searchDTO.Filters.DateFrom.Value);
    }

    if (searchDTO.Filters.DateTo.HasValue)
    {
      lotsQuery = lotsQuery.Where(lot => lot.Updated <= searchDTO.Filters.DateTo.Value);
    }

    // Apply search term filter if provided
    if (!string.IsNullOrEmpty(searchDTO.Filters.SearchTerm))
    {
      var searchTerm = searchDTO.Filters.SearchTerm.ToLower();
      lotsQuery = lotsQuery.Where(lot =>
          (lot.Vrm != null && EF.Functions.Like(lot.Vrm.ToLower(), $"%{searchTerm}%")) ||
          (lot.Manufacturer != null && EF.Functions.Like(lot.Manufacturer.ToLower(), $"%{searchTerm}%")) ||
          (lot.Model != null && EF.Functions.Like(lot.Model.ToLower(), $"%{searchTerm}%")));
    }

    // Get total count
    var totalItems = await lotsQuery.CountAsync(cancellationToken);

    // Apply sorting if specified
    if (searchDTO.Order != null && searchDTO.Order.Any())
    {
      var firstOrderBy = searchDTO.Order.First();
      lotsQuery = ApplySortingToLots(lotsQuery, firstOrderBy);
    }
    else
    {
      // Default sorting by update date
      lotsQuery = lotsQuery.OrderByDescending(lot => lot.Updated);
    }

    // Apply pagination
    if (searchDTO.Offset.HasValue && searchDTO.Limit.HasValue)
    {
      lotsQuery = lotsQuery
          .Skip(searchDTO.Offset.Value)
          .Take(searchDTO.Limit.Value);
    }

    // Execute the query
    var lots = await lotsQuery.ToListAsync(cancellationToken);

    // Map to DTOs
    var result = lots.Select(lot => new InventAuctionLotDTO
    {
      Id = lot.Id,
      AuctionId = lot.AuctionId,
      Vrm = lot.Vrm,
      Vin = lot.Vin,
      Manufacturer = lot.Manufacturer,
      Model = lot.Model,
      Variant = lot.Variant,
      Year = lot.Year,
      Mileage = lot.Mileage,
      BodyType = lot.BodyType,
      Colour = lot.Colour,
      FuelType = lot.FuelType,
      Transmission = lot.Transmission,
      ReservePrice = lot.ReservePrice,
      CurrentBid = lot.CurrentBid,
      Status = lot.Status,
      AuctionTitle = lot.Auction?.Title,
      AuctionDateTime = lot.Auction?.DateTime,
      DefaultImageUrl = GetPlaceholderImageUrl(),
      ImageUrls = lot.Images.OrderBy(x => x.ImageUrl).Select(x => x.ImageUrl).ToList(),
      ProcessedDate = lot.Added
    }).ToList();

    return new SearchResultDTO<InventAuctionLotDTO>
    {
      Results = result,
      TotalItems = totalItems
    };
  }

  /// <summary>
  /// Applies sorting to lot query
  /// </summary>
  private IQueryable<InventAuctionLot> ApplySortingToLots(
      IQueryable<InventAuctionLot> query, OrderByDTO orderBy)
  {
    if (orderBy == null)
      return query;

    var isDescending = orderBy.Descending ?? false;

    switch (orderBy.Column?.ToLower())
    {
      case "vrm":
        return isDescending
            ? query.OrderByDescending(l => l.Vrm)
            : query.OrderBy(l => l.Vrm);
      case "vin":
        return isDescending
            ? query.OrderByDescending(l => l.Vin)
            : query.OrderBy(l => l.Vin);
      case "make":
      case "manufacturer":
        return isDescending
            ? query.OrderByDescending(l => l.Manufacturer)
            : query.OrderBy(l => l.Manufacturer);
      case "model":
        return isDescending
            ? query.OrderByDescending(l => l.Model)
            : query.OrderBy(l => l.Model);
      case "variant":
      case "derivative":
        return isDescending
            ? query.OrderByDescending(l => l.Variant)
            : query.OrderBy(l => l.Variant);
      case "year":
        return isDescending
            ? query.OrderByDescending(l => l.Year)
            : query.OrderBy(l => l.Year);
      case "price":
      case "reserveprice":
        return isDescending
            ? query.OrderByDescending(l => l.ReservePrice)
            : query.OrderBy(l => l.ReservePrice);
      case "status":
        return isDescending
            ? query.OrderByDescending(l => l.Status)
            : query.OrderBy(l => l.Status);
      case "updated":
      case "processeddate":
        return isDescending
            ? query.OrderByDescending(l => l.Updated)
            : query.OrderBy(l => l.Updated);
      case "auction":
      case "auctiontitle":
        return isDescending
            ? query.OrderByDescending(l => l.Auction.Title)
            : query.OrderBy(l => l.Auction.Title);
      case "auctiondate":
        return isDescending
            ? query.OrderByDescending(l => l.Auction.DateTime)
            : query.OrderBy(l => l.Auction.DateTime);
      default:
        return isDescending
            ? query.OrderByDescending(l => l.Updated)
            : query.OrderBy(l => l.Updated);
    }
  }

  /// <summary>
  /// Searches for available auction lots
  /// </summary>
  private async Task<SearchResultDTO<InventAuctionLotDTO>> SearchAvailableLotsAsync(
      InventSearchDTO searchDTO, CancellationToken cancellationToken)
  {
    // Get auction lots that are available (status 1 or 6) and not yet linked to vehicles
    var lotsQuery = _tradingContext.InventAuctionLots
        .Include(x => x.Images)
        .Include(x => x.Auction)
        .Where(lot => (lot.Status == (int)InventAuctionLotStatus.Available || lot.Status == (int)InventAuctionLotStatus.Error) && lot.VehicleId == null) 
        .AsQueryable();

    if (searchDTO.Filters.InventUserId.HasValue)
    {
      lotsQuery = lotsQuery.Where(lot => lot.Auction.InventUserId == searchDTO.Filters.InventUserId.Value);
    }

    if (!string.IsNullOrEmpty(searchDTO.Filters.SearchTerm))
    {
      var searchTerm = searchDTO.Filters.SearchTerm.ToLower();
      lotsQuery = lotsQuery.Where(lot =>
          (lot.Vrm != null && EF.Functions.Like(lot.Vrm.ToLower(), $"%{searchTerm}%")) ||
          (lot.Manufacturer != null && EF.Functions.Like(lot.Manufacturer.ToLower(), $"%{searchTerm}%")) ||
          (lot.Model != null && EF.Functions.Like(lot.Model.ToLower(), $"%{searchTerm}%")) ||
          (lot.Variant != null && EF.Functions.Like(lot.Variant.ToLower(), $"%{searchTerm}%")));
    }

    var totalItems = await lotsQuery.CountAsync(cancellationToken);

    if (searchDTO.Order != null && searchDTO.Order.Any())
    {
      var firstOrderBy = searchDTO.Order.First();
      lotsQuery = ApplySortingToLots(lotsQuery, firstOrderBy);
    }
    else
    {
      lotsQuery = lotsQuery.OrderBy(x => x.Updated.Value.Date).ThenBy(lot => lot.Vrm);
    }

    if (searchDTO.Offset.HasValue && searchDTO.Limit.HasValue)
    {
      lotsQuery = lotsQuery
          .Skip(searchDTO.Offset.Value)
          .Take(searchDTO.Limit.Value);
    }

    var lots = await lotsQuery.ToListAsync(cancellationToken);

    var result = lots.Select(lot => new InventAuctionLotDTO
    {
      Id = lot.Id,
      AuctionId = lot.AuctionId,
      InventVehicleId = lot.InventVehicleId,
      Vrm = lot.Vrm,
      Vin = lot.Vin,
      Manufacturer = lot.Manufacturer,
      Model = lot.Model,
      Variant = lot.Variant,
      Year = lot.Year,
      Mileage = lot.Mileage,
      BodyType = lot.BodyType,
      Colour = lot.Colour,
      FuelType = lot.FuelType,
      Transmission = lot.Transmission,
      ReservePrice = lot.ReservePrice,
      CurrentBid = lot.CurrentBid,
      Status = lot.Status,
      AuctionTitle = lot.Auction?.Title,
      AuctionDateTime = lot.Auction?.DateTime,
      DefaultImageUrl = GetPlaceholderImageUrl(),
      ImageUrls = lot.Images.OrderBy(x => x.ImageUrl).Select(x => x.ImageUrl).ToList(),
      ProcessedDate = null,
      ImportTextError = lot.ImportErrorText,
      Added = lot.Added,
      Updated = lot.Updated
    })
      .ToList();

    return new SearchResultDTO<InventAuctionLotDTO>
    {
      Results = result,
      TotalItems = totalItems
    };
  }

  /// <summary>
  /// Searches for imported lots
  /// </summary>
  private async Task<SearchResultDTO<InventAuctionLotDTO>> SearchImportedLotsAsync(
      InventSearchDTO searchDTO, CancellationToken cancellationToken)
  {
    // Get auction lots that have been processed (status 2) with their vehicles and adverts
    var lotsQuery = _tradingContext.InventAuctionLots
        .Include(x => x.Images)
        .Include(x => x.Auction)
        .Include(x => x.Vehicle)
          .ThenInclude(v => v.Adverts.Where(a => a.StatusId == (uint)StatusEnum.Active))
        .Where(lot => lot.Status == (int)InventAuctionLotStatus.Imported && lot.VehicleId != null) // Direct vehicle link check
        .AsQueryable();

    if (searchDTO.Filters.InventUserId.HasValue)
    {
      lotsQuery = lotsQuery.Where(lot => lot.Auction.InventUserId == searchDTO.Filters.InventUserId.Value);
    }

    if (searchDTO.Filters.DateFrom.HasValue)
    {
      lotsQuery = lotsQuery.Where(lot => lot.Updated >= searchDTO.Filters.DateFrom.Value);
    }

    if (searchDTO.Filters.DateTo.HasValue)
    {
      lotsQuery = lotsQuery.Where(lot => lot.Updated <= searchDTO.Filters.DateTo.Value);
    }

    if (!string.IsNullOrEmpty(searchDTO.Filters.SearchTerm))
    {
      var searchTerm = searchDTO.Filters.SearchTerm.ToLower();
      lotsQuery = lotsQuery.Where(lot =>
          (lot.Vrm != null && EF.Functions.Like(lot.Vrm.ToLower(), $"%{searchTerm}%")) ||
          (lot.Vin != null && EF.Functions.Like(lot.Vin.ToLower(), $"%{searchTerm}%")) ||
          (lot.Manufacturer != null && EF.Functions.Like(lot.Manufacturer.ToLower(), $"%{searchTerm}%")) ||
          (lot.Model != null && EF.Functions.Like(lot.Model.ToLower(), $"%{searchTerm}%")));
    }

    var totalItems = await lotsQuery.CountAsync(cancellationToken);

    if (searchDTO.Order != null && searchDTO.Order.Any())
    {
      var firstOrderBy = searchDTO.Order.First();
      lotsQuery = ApplySortingToLots(lotsQuery, firstOrderBy);
    }
    else
    {
      lotsQuery = lotsQuery.OrderByDescending(lot => lot.Updated);
    }

    if (searchDTO.Offset.HasValue && searchDTO.Limit.HasValue)
    {
      lotsQuery = lotsQuery
          .Skip(searchDTO.Offset.Value)
          .Take(searchDTO.Limit.Value);
    }

    var lots = await lotsQuery.ToListAsync(cancellationToken);

    // Map to DTOs - much simpler now with direct relationships
    var result = lots.Select(lot =>
    {
      var latestAdvert = lot.Vehicle?.Adverts?.OrderByDescending(a => a.Updated).FirstOrDefault();

      return new InventAuctionLotDTO
      {
        Id = lot.Id,
        AuctionId = lot.AuctionId,
        InventVehicleId = lot.InventVehicleId,
        VehicleId = lot.VehicleId,
        Vrm = lot.Vrm,
        Vin = lot.Vin,
        Manufacturer = lot.Manufacturer,
        Model = lot.Model,
        Variant = lot.Variant,
        Year = lot.Year,
        Mileage = lot.Mileage,
        BodyType = lot.BodyType,
        Colour = lot.Colour,
        FuelType = lot.FuelType,
        Transmission = lot.Transmission,
        ReservePrice = lot.ReservePrice,
        CurrentBid = latestAdvert?.TopBid?.BidAmt,
        Status = lot.Status,
        AuctionTitle = lot.Auction?.Title,
        AuctionDateTime = lot.Auction?.DateTime,
        DefaultImageUrl = GetPlaceholderImageUrl(),
        ImageUrls = lot.Images.OrderBy(x => x.ImageUrl).Select(x => x.ImageUrl).ToList(),
        ProcessedDate = lot.Updated,
        EndDateTime = latestAdvert?.EndDateTime,
        AdvertId = latestAdvert?.Id,
      };
    }).ToList();

    return new SearchResultDTO<InventAuctionLotDTO>
    {
      Results = result,
      TotalItems = totalItems
    };
  }

  /// <summary>
  /// Searches for sold lots
  /// </summary>
  private async Task<SearchResultDTO<InventAuctionLotDTO>> SearchSoldLotsAsync(
      InventSearchDTO searchDTO, CancellationToken cancellationToken)
  {
    bool recentOnly = false;
    if (searchDTO.Filters.DateFrom.HasValue)
    {
      var cutoffDate = DateTime.Now.AddDays(-7);
      recentOnly = searchDTO.Filters.DateFrom.Value >= cutoffDate;
    }

    // Get lots that have been processed and have vehicles with sold adverts
    var lotsQuery = _tradingContext.InventAuctionLots
        .Include(x => x.Images)
        .Include(x => x.Auction)
        .Include(x => x.Vehicle)
          .ThenInclude(v => v.Adverts.Where(a => a.SoldStatus == SoldStatusEnum.Sold))
        .Where(lot => lot.Status == (int)InventAuctionLotStatus.Imported &&
                     lot.VehicleId != null &&
                     lot.Vehicle.Adverts.Any(a => a.SoldStatus == SoldStatusEnum.Sold))
        .AsQueryable();

    if (searchDTO.Filters.InventUserId.HasValue)
    {
      lotsQuery = lotsQuery.Where(lot => lot.Auction.InventUserId == searchDTO.Filters.InventUserId.Value);
    }

    if (!string.IsNullOrEmpty(searchDTO.Filters.SearchTerm))
    {
      var searchTerm = searchDTO.Filters.SearchTerm.ToLower();
      lotsQuery = lotsQuery.Where(lot =>
          (lot.Vrm != null && EF.Functions.Like(lot.Vrm.ToLower(), $"%{searchTerm}%")) ||
          (lot.Vin != null && EF.Functions.Like(lot.Vin.ToLower(), $"%{searchTerm}%")) ||
          (lot.Manufacturer != null && EF.Functions.Like(lot.Manufacturer.ToLower(), $"%{searchTerm}%")) ||
          (lot.Model != null && EF.Functions.Like(lot.Model.ToLower(), $"%{searchTerm}%")));
    }

    // Apply date filters on sold date if provided
    if (searchDTO.Filters.DateFrom.HasValue)
    {
      lotsQuery = lotsQuery.Where(lot =>
          lot.Vehicle.Adverts.Any(a => a.SoldStatus == SoldStatusEnum.Sold &&
                                     a.EndDateTime >= searchDTO.Filters.DateFrom.Value));
    }

    if (searchDTO.Filters.DateTo.HasValue)
    {
      lotsQuery = lotsQuery.Where(lot =>
          lot.Vehicle.Adverts.Any(a => a.SoldStatus == SoldStatusEnum.Sold &&
                                     a.EndDateTime <= searchDTO.Filters.DateTo.Value));
    }

    var totalItems = await lotsQuery.CountAsync(cancellationToken);

    if (searchDTO.Order != null && searchDTO.Order.Any())
    {
      var firstOrderBy = searchDTO.Order.First();
      lotsQuery = ApplySortingToLotsWithSoldAdverts(lotsQuery, firstOrderBy);
    }
    else
    {
      // Default sorting by sold date (most recent first)
      lotsQuery = lotsQuery.OrderByDescending(lot =>
          lot.Vehicle.Adverts
              .Where(a => a.SoldStatus == SoldStatusEnum.Sold)
              .Max(a => a.EndDateTime));
    }

    if (searchDTO.Offset.HasValue && searchDTO.Limit.HasValue)
    {
      lotsQuery = lotsQuery
          .Skip(searchDTO.Offset.Value)
          .Take(searchDTO.Limit.Value);
    }

    var lots = await lotsQuery.ToListAsync(cancellationToken);

    // Map to DTOs - much cleaner with direct relationships
    var result = lots.Select(lot =>
    {
      var soldAdvert = lot.Vehicle?.Adverts?
          .Where(a => a.SoldStatus == SoldStatusEnum.Sold)
          .OrderByDescending(a => a.Updated)
          .FirstOrDefault();

      return new InventAuctionLotDTO
      {
        Id = lot.Id,
        AuctionId = lot.AuctionId,
        VehicleId = lot.VehicleId,
        InventVehicleId = lot.InventVehicleId,
        Vrm = lot.Vrm,
        Vin = lot.Vin,
        Manufacturer = lot.Manufacturer,
        Model = lot.Model,
        Variant = lot.Variant,
        Year = lot.Year,
        Mileage = lot.Mileage,
        BodyType = lot.BodyType,
        Colour = lot.Colour,
        FuelType = lot.FuelType,
        Transmission = lot.Transmission,
        ReservePrice = lot.ReservePrice,
        CurrentBid = soldAdvert?.SoldPrice ?? soldAdvert?.TopBid?.BidAmt,
        Status = lot.Status,
        AuctionTitle = lot.Auction?.Title,
        AuctionDateTime = lot.Auction?.DateTime,
        DefaultImageUrl = GetPlaceholderImageUrl(),
        ImageUrls = lot.Images.OrderBy(x => x.ImageUrl).Select(x => x.ImageUrl).ToList(),
        ProcessedDate = lot.Updated,
        SoldDate = soldAdvert?.EndDateTime,
        EndDateTime = soldAdvert?.EndDateTime,
        AdvertId = soldAdvert?.Id,
        RecentlySold = recentOnly
      };
    }).ToList();

    return new SearchResultDTO<InventAuctionLotDTO>
    {
      Results = result,
      TotalItems = totalItems
    };
  }

  /// <summary>
  /// Helper method to apply sorting to sold lots - replaces the complex list-based sorting
  /// </summary>
  private IQueryable<InventAuctionLot> ApplySortingToLotsWithSoldAdverts(
      IQueryable<InventAuctionLot> query, OrderByDTO orderBy)
  {
    if (orderBy == null)
      return query;

    var isDescending = orderBy.Descending ?? false;

    return orderBy.Column?.ToLower() switch
    {
      "solddate" => isDescending
          ? query.OrderByDescending(lot =>
              lot.Vehicle.Adverts.Where(a => a.SoldStatus == SoldStatusEnum.Sold).Max(a => a.EndDateTime))
          : query.OrderBy(lot =>
              lot.Vehicle.Adverts.Where(a => a.SoldStatus == SoldStatusEnum.Sold).Min(a => a.EndDateTime)),
      "soldprice" or "price" => isDescending
          ? query.OrderByDescending(lot =>
              lot.Vehicle.Adverts.Where(a => a.SoldStatus == SoldStatusEnum.Sold).Max(a => a.SoldPrice ?? a.TopBid.BidAmt))
          : query.OrderBy(lot =>
              lot.Vehicle.Adverts.Where(a => a.SoldStatus == SoldStatusEnum.Sold).Min(a => a.SoldPrice ?? a.TopBid.BidAmt)),
      _ => ApplySortingToLots(query, orderBy)
    };
  }

  public async Task<int> ProcessAuctionLotsAsync(Guid inventUserId, IEnumerable<Guid> selectedLotIds = null, CancellationToken cancellationToken = default)
  {
    try
    {
      _logger.LogInformation($"Starting to process auction lots for invent user {inventUserId}");

      // Get auction and user data
      var (auction, inventUser) = await GetAuctionAndUserDataAsync(inventUserId, cancellationToken);
      if (auction == null || inventUser == null)
      {
        return 0;
      }

      // Get lots to process
      var lots = await GetLotsToProcessAsync(auction.Id, selectedLotIds, cancellationToken);
      if (lots.Count == 0)
      {
        _logger.LogWarning($"No unprocessed lots found for auction {auction.Id}");
        return 0;
      }

      var result = await ProcessLotsAsync(lots, inventUser, cancellationToken);
      await FinalizeAuctionIfCompleteAsync(auction, cancellationToken);

      _logger.LogInformation($"Auction {auction.Id} processing completed. {result.SuccessCount} vehicles imported successfully.");

      if (result.ErrorMessages.Any())
      {
        _logger.LogWarning($"Errors during processing: {string.Join("; ", result.ErrorMessages)}");
      }

      return result.SuccessCount;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, $"Error processing auction for invent user {inventUserId}");
      throw;
    }
  }

  private async Task<(InventAuction auction, InventUser inventUser)> GetAuctionAndUserDataAsync(Guid inventUserId, CancellationToken cancellationToken)
  {
    var auction = await _tradingContext.InventAuctions
        .FirstOrDefaultAsync(a => a.InventUserId == inventUserId, cancellationToken);

    if (auction == null)
    {
      _logger.LogWarning($"Auction for user {inventUserId} not found");
      return (null, null);
    }

    var inventUser = await _tradingContext.InventUsers
        .AsNoTracking()
        .FirstOrDefaultAsync(x => x.Id == inventUserId, cancellationToken);

    if (inventUser == null)
    {
      _logger.LogWarning($"Invent user with ID {inventUserId} not found");
      return (null, null);
    }

    return (auction, inventUser);
  }

  private async Task<List<InventAuctionLot>> GetLotsToProcessAsync(Guid auctionId, IEnumerable<Guid> selectedLotIds, CancellationToken cancellationToken)
  {
    var lotsQuery = _tradingContext.InventAuctionLots
        .Include(x => x.Images)
        .Where(lot => (lot.Status == (int)InventAuctionLotStatus.Available || lot.Status == (int)InventAuctionLotStatus.Error) &&
                     lot.VehicleId == null);

    if (selectedLotIds != null && selectedLotIds.Any())
    {
      lotsQuery = lotsQuery.Where(lot => selectedLotIds.Contains(lot.Id));
    }

    return await lotsQuery.ToListAsync(cancellationToken);
  }

  private async Task<ProcessingResult> ProcessLotsAsync(List<InventAuctionLot> lots, InventUser inventUser, CancellationToken cancellationToken)
  {
    var result = new ProcessingResult();

    foreach (var lot in lots)
    {
      var lotId = lot.Id;
      var lotIdentifier = GetLotIdentifier(lot);

      try
      {
        _logger.LogInformation($"Processing lot {lotId} ({lotIdentifier})");

        await ProcessSingleLotAsync(lot, inventUser, cancellationToken);
        result.SuccessCount++;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, $"Error processing lot {lotId} ({lotIdentifier})");

        await HandleLotProcessingErrorAsync(lotId, ex.Message, cancellationToken);
        result.ErrorMessages.Add(ex.Message);
      }
    }

    return result;
  }

  private async Task ProcessSingleLotAsync(InventAuctionLot lot, InventUser inventUser, CancellationToken cancellationToken)
  {
    // Setup vehicle
    var vehicle = await GetOrCreateVehicleAsync(lot, inventUser, cancellationToken);
    lot.VehicleId = vehicle.Id;

    // Fetch vehicle data from auction API
    var (inspectionResult, features, buyNowPrice, serviceHistory) = await FetchVehicleDataAsync(lot.InventVehicleId.ToString());

    // Process inspection data and save appraisal
    if (inspectionResult != null && inspectionResult.DamageItems.Any())
    {
      var appraisal = await ConvertInspectionToAppraisal(inspectionResult, inventUser.CustomerRef, vehicle.Id, lot);
      await SaveInspectionTyreDataAsync(vehicle.Id, inspectionResult.TyreItems);
    }

    if (serviceHistory!= null && serviceHistory.ServiceHistoryItems.Any())
    {
      // Add service history processing
      await SaveServiceHistoryDataAsync(vehicle.Id, serviceHistory);
    }

    // Create or update advert
    var advertId = await CreateOrUpdateAdvertAsync(lot, vehicle.Id, inventUser, cancellationToken);

    // Update advert with notes and features
    await UpdateAdvertWithNotesAsync(advertId, lot, inspectionResult, features, inventUser, serviceHistory, cancellationToken);

    if (serviceHistory?.LastServiceDate.HasValue == true)
    {
      var patch = new JsonPatchDocument<Vehicle>();
      patch.Add(x => x.ServiceHistory, true);
      patch.Add(x => x.ServiceHistoryType, ServiceHistoryTypeEnum.Yes);
      await _vehicleService.Patch(vehicle.Id, inventUser.CustomerId, patch, cancellationToken);
    }

    // Configure Buy Now if available
    var priceToUse = lot.ReservePrice ?? buyNowPrice;

    if (priceToUse.HasValue)
    {
      var buyNowSaleId = await _saleService.GetBuyNowSaleId(cancellationToken);
      var patch = new JsonPatchDocument<Advert>();
      patch.Add(x => x.BuyItNowPrice, (uint?)(priceToUse + inventUser.AuctionMarkup));
      patch.Add(x => x.SaleId, buyNowSaleId);
      patch.Add(x => x.AcceptBids, true);
      await _advertService.Patch(advertId, inventUser.CustomerId, patch, cancellationToken);
    }

    // Process images and publish if available
    if (lot.Images?.Any() == true)
    {
      await ProcessLotImagesAsync(lot, vehicle.Id, inventUser, cancellationToken);

      try
      {
        await _advertService.PublishAdvert(inventUser.ContactId, advertId, cancellationToken);
      }
      catch (Exception ex)
      {
        // we can ignore the publishing error if it's that vehicle already published 
        if (ex.Message.Contains("AD12", StringComparison.OrdinalIgnoreCase))
        {
          _logger.LogWarning($"Advert for lot {lot.Id} ({GetLotIdentifier(lot)}) is already published.");
        }
        else
          throw;
      }
    }

    // Finalize
    lot.Status = 2;
    await _tradingContext.SaveChangesAsync(cancellationToken);
  }

  private async Task<(InventInspectionResult inspectionResult, IEnumerable<string> features, decimal? buyNowPrice, InventServiceHistoryResult serviceHistoryResult)> FetchVehicleDataAsync(string inventVehicleId)
  {
    var inspectionHtml = await _auctionApiClient.GetVehicleInspectionsHtmlAsync(inventVehicleId);
    var inspectionId = await _auctionApiClient.GetInspectionIdFromHtmlAsync(inspectionHtml);
    var inspectionResultHtml = await _auctionApiClient.GetInspectionHtmlAsync(inventVehicleId, inspectionId);
    var inspectionResult = await _auctionApiClient.GetInspectionResultFromHtmlAsync(inspectionResultHtml);

    var motorVehicleHtml = await _auctionApiClient.GetMotorVehicleInformationHtmlAsync(inventVehicleId);
    var features = await _auctionApiClient.GetFeaturesFromHtmlAsync(motorVehicleHtml);
    var buyNowPrice = await _auctionApiClient.GetBuyNowFromHtmlAsync(motorVehicleHtml);
    var serviceHistory = await _auctionApiClient.GetServiceHistoryFromHTML(motorVehicleHtml);

    return (inspectionResult, features, buyNowPrice, serviceHistory);
  }

  private async Task SaveServiceHistoryDataAsync(Guid vehicleId, InventServiceHistoryResult serviceHistoryResult)
  {
    // Remove existing service history records for this vehicle
    var existingServiceHistory = await _tradingContext.ServiceHistories
        .Where(x => x.VehicleId == vehicleId)
        .ToListAsync();

    _tradingContext.ServiceHistories.RemoveRange(existingServiceHistory);

    // Add new service history records
    var newServiceRecords = new List<ServiceHistory>();

    foreach (var serviceItem in serviceHistoryResult.ServiceHistoryItems)
    {
      // Skip records with no meaningful data
      if (serviceItem.ServiceDate == default(DateTime) &&
          serviceItem.Odometer == 0 &&
          string.IsNullOrEmpty(serviceItem.DealerName))
        continue;

      newServiceRecords.Add(new ServiceHistory
      {
        VehicleId = vehicleId,
        ServiceDate = serviceItem.ServiceDate != default(DateTime) ? serviceItem.ServiceDate : DateTime.MinValue,
        Odometer = serviceItem.Odometer,
        DealerName = string.IsNullOrWhiteSpace(serviceItem.DealerName) ? null : serviceItem.DealerName.Trim(),
        DealerType = MapDealerType(serviceItem.DealerType),
        Added = DateTime.Now,
        Updated = DateTime.Now,
        StatusId = (uint)StatusEnum.Active,
      });
    }

    if (newServiceRecords.Any())
    {
      await _tradingContext.ServiceHistories.AddRangeAsync(newServiceRecords);
      await _tradingContext.SaveChangesAsync();
    }
  }

  private DealerTypeEnum MapDealerType(string dealerTypeString)
  {
    if (string.IsNullOrWhiteSpace(dealerTypeString))
      return DealerTypeEnum.Other; // Default

    return dealerTypeString.ToLower().Trim() switch
    {
      "dealer" => DealerTypeEnum.Franchise,
      "specialist" => DealerTypeEnum.Specialist,
      "chain" => DealerTypeEnum.Chain,
      "independent" => DealerTypeEnum.Independent,
      "other" => DealerTypeEnum.Other,
      _ => DealerTypeEnum.Other // Default
    };
  }

  private async Task UpdateAdvertWithNotesAsync(Guid advertId, InventAuctionLot lot, InventInspectionResult inspectionResult,
      IEnumerable<string> features, InventUser inventUser, InventServiceHistoryResult serviceHistoryResult, CancellationToken cancellationToken)
  {
    var notesBuilder = new StringBuilder();

    // SERVICE HISTORY (Priority 1 - Critically Important)
    if (serviceHistoryResult.LastServiceDate.HasValue)
    {
      notesBuilder.AppendLine("SERVICE HISTORY:");
      notesBuilder.AppendLine($"Last Service Date: {serviceHistoryResult.LastServiceDate.Value.ToShortDateString()}" +
        $" - Service Mileage: {serviceHistoryResult.LastServiceMiles:N0} miles" +
        $" - Service Book Stamps: {serviceHistoryResult.ServiceBookNumStamps}");
      notesBuilder.AppendLine();
    }

    if (!string.IsNullOrEmpty(lot.NamaGrade))
    {
      notesBuilder.AppendLine($"NAMA GRADE: {lot.NamaGrade}");
      notesBuilder.AppendLine();
    }

    // INSPECTION NOTES (Priority 2)
    if (!string.IsNullOrWhiteSpace(inspectionResult?.InteriorNotes))
    {
      notesBuilder.AppendLine("INSPECTION NOTES: ");
      notesBuilder.AppendLine($"{inspectionResult.InteriorNotes}");
      notesBuilder.AppendLine();
    }

    // VEHICLE FEATURES (Priority 3)
    if (features?.Any() == true)
    {
      notesBuilder.AppendLine("KEY FEATURES:");
      notesBuilder.AppendLine($"{string.Join(" • ", features)}");
      notesBuilder.AppendLine();
    }

    // Only update if we have notes to add
    if (notesBuilder.Length > 0)
    {
      var finalNotes = notesBuilder.ToString().TrimEnd();
      var patch = new JsonPatchDocument<Advert>();
      patch.Add(x => x.Description, finalNotes);
      await _advertService.Patch(advertId, inventUser.CustomerId, patch, cancellationToken);
    }
  }

  private async Task<Appraisal> ConvertInspectionToAppraisal(InventInspectionResult inspectionData, string customerRef, Guid vehicleId, InventAuctionLot lot)
  {
    // Convert inspection data to Appraisal object
    var appraisal = new Appraisal
    {
      Added = DateTime.Now,
      Updated = DateTime.Now,
      StatusId = (uint)StatusEnum.Active,
      AppraisalDate = inspectionData.InspectionDate,
      AppraisalRef = customerRef + ":" + lot.InventVehicleId,
      VehicleId = vehicleId,
      AppraisalItems = inspectionData.DamageItems.Select(x => new AppraisalItem
      {
        Added = DateTime.Now,
        Updated = DateTime.Now,
        StatusId = (uint)StatusEnum.Active,
        ItemDesc = x.Condition,
        ItemLocation = x.Component,
        RepairDesc = x.Severity,
        SourceType = "CityInspection",
        AppraisalMedia = new List<AppraisalMedia>
        {
          new AppraisalMedia {
            Added = DateTime.Now,
            Updated = DateTime.Now,
            StatusId = (uint)StatusEnum.Active,
            MediaTypeId = (uint)MediaTypeEnum.Image,
            CustomerRef = $"{customerRef}{ExtractInspectionImagePath(x.ImageUrl)}"
          }
        }
      }).ToList(),
    };

    _tradingContext.Appraisals.Add(appraisal);
    await _tradingContext.SaveChangesAsync(CancellationToken.None);

    return appraisal;
  }

  public async Task SaveInspectionTyreDataAsync(Guid vehicleId, List<InventTyreItem> tyreItems)
  {
    // Remove existing tyre records for this vehicle
    var existingTyres = await _tradingContext.VehicleTyreInfos
        .Where(x => x.VehicleId == vehicleId)
        .ToListAsync();

    _tradingContext.VehicleTyreInfos.RemoveRange(existingTyres);

    // Add new tyre records
    var newTyreRecords = new List<VehicleTyreInfo>();

    foreach (var tyre in tyreItems)
    {
      var normalizedPosition = TyrePositionMapper.NormalizePosition(tyre.Position);

      // Skip invalid positions or completely empty records
      if (normalizedPosition == null)
        continue;

      // Skip records where both make and condition are empty/null
      if (string.IsNullOrWhiteSpace(tyre.Make) && string.IsNullOrWhiteSpace(tyre.Condition))
        continue;

      newTyreRecords.Add(new VehicleTyreInfo
      {
        VehicleId = vehicleId,
        Position = normalizedPosition.Value,
        Make = string.IsNullOrWhiteSpace(tyre.Make) ? null : tyre.Make.Trim(),
        Condition = string.IsNullOrWhiteSpace(tyre.Condition) ? null : tyre.Condition.Trim(),
        Added = DateTime.Now,
        Updated = DateTime.Now,
        StatusId = (uint)StatusEnum.Active,
      });
    }

    if (newTyreRecords.Any())
    {
      await _tradingContext.VehicleTyreInfos.AddRangeAsync(newTyreRecords);
      await _tradingContext.SaveChangesAsync();
    }
  }

  private string ExtractInspectionImagePath(string fullUrl)
  {
    if (string.IsNullOrEmpty(fullUrl))
      return "";

    var inspectionImagesIndex = fullUrl.IndexOf("/inspectionimages", StringComparison.OrdinalIgnoreCase);
    
    if (inspectionImagesIndex >= 0)
    {
      return fullUrl.Substring(inspectionImagesIndex);
    } 
    else
    {
      var motorVehicleImagesIndex = fullUrl.IndexOf("/motorvehicle", StringComparison.OrdinalIgnoreCase);

      if (motorVehicleImagesIndex >= 0)
      {
        return fullUrl.Substring(motorVehicleImagesIndex);
      }
    }

    return "";
  }

  private async Task<Vehicle> GetOrCreateVehicleAsync(InventAuctionLot lot, InventUser inventUser, CancellationToken cancellationToken)
  {
    var vehicle = await FindExistingVehicleAsync(lot.Vin, lot.Vrm, cancellationToken);

    if (vehicle != null)
    {
      // set provenance and valuation 
      await _vehicleCheckService.UpdateVehicleChecks(new VRMLookupDataDTO { vehicleId = vehicle.Id }, cancellationToken);

      return vehicle;
    }

    // Create new vehicle
    var vehicleDTO = await _vehicleService.CreateVehicle(new ExternalDTO.CreateVehicleDTO
    {
      LogBook = lot.HasV5,
      Vrm = lot.Vrm,
      Vin = lot.Vin,
      AddressId = inventUser.AddressId,
      ContactId = inventUser.ContactId,
      CustomerId = inventUser.CustomerId,
      Odometer = lot.Mileage
    }, cancellationToken);

    vehicle = await _tradingContext.Vehicles
        .FirstOrDefaultAsync(x => x.Id == vehicleDTO.Id, cancellationToken);

    // Update additional vehicle properties
    vehicle.Colour = lot.Colour;
    vehicle.VatStatusId = DetermineVatStatusId(lot.VatStatus);
    vehicle.ServiceHistory = lot.ServiceHistory;

    _tradingContext.Update(vehicle);
    await _tradingContext.SaveChangesAsync(cancellationToken);

    // set provenance and valuation 
    await _vehicleCheckService.UpdateVehicleChecks(new VRMLookupDataDTO { vehicleId = vehicle.Id }, cancellationToken);

    return vehicle;
  }

  private uint DetermineVatStatusId(string vatStatus)
  {
    if (string.IsNullOrWhiteSpace(vatStatus))
    {
      return 3; // Default: NO VAT
    }

    var normalized = vatStatus.Trim().ToUpperInvariant();
    return normalized switch
    {
      "VAT QUALIFYING" => 2,
      "PLUS VAT" => 15,
      "NO VAT" => 3,
      _ => 3 // Default: NO VAT
    };
  }

  private async Task<Guid> CreateOrUpdateAdvertAsync(InventAuctionLot lot, Guid vehicleId, InventUser inventUser, CancellationToken cancellationToken)
  {
    var advert = await _tradingContext.Adverts
        .FirstOrDefaultAsync(x => x.VehicleId == vehicleId, cancellationToken);

    if (advert != null)
    {
      // Update existing advert
      advert.AvailableDate = DateTime.Now;
      advert.EndDateTime = DateTime.Now.AddDays(7);
      advert.Updated = DateTime.Now;
      advert.ReservePrice = (uint)(lot?.ReservePrice ?? 0);

      await _tradingContext.SaveChangesAsync(cancellationToken);
      return advert.Id;
    }

    // Create new advert
    var advertDTO = await _advertService.CreateAdvert(
        new CreateAdvertDTO
        {
          VehicleId = vehicleId,
          ContactId = inventUser.ContactId,
          CustomerId = inventUser.CustomerId,
          ContactAddressId = inventUser.AddressId,
          ReservePrice = (uint?)lot.ReservePrice,
        }, cancellationToken);

    return advertDTO.Id.Value;
  }

  private async Task ProcessLotImagesAsync(InventAuctionLot lot, Guid vehicleId, InventUser inventUser, CancellationToken cancellationToken)
  {
    var allImages = lot.Images
        .OrderBy(x => x.ImageUrl)
        .Select(x => new VehicleMediaURLContent
        {
          IsInternal = false,
          MediaType = MediaTypeEnum.Image,
          Url = x.ImageUrl,
          Tag = x.AuctionLot.LotId.ToString(),
        }).ToList();

    if (inventUser.LogoSwapPixelHeight > 0)
    {
      // Use the new batched parallel approach
      await _mediaService.UploadMediaFromURLsWithLogoSwapBatched(
          inventUser.CustomerId,
          new VehicleMediaUploadDTO
          {
            VehicleId = vehicleId,
            MediaURLContent = allImages
          },
          inventUser.LogoSwapPixelHeight,
          cancellationToken);
    }
    else
    {
      await ProcessImagesWithoutLogoSwapAsync(allImages, vehicleId, inventUser.CustomerId, cancellationToken);
    }
  }

  private async Task ProcessImagesWithoutLogoSwapAsync(List<VehicleMediaURLContent> images, Guid vehicleId, Guid customerId, CancellationToken cancellationToken)
  {
    await _mediaService.UploadMediaFromURLs(
        customerId,
        new VehicleMediaUploadDTO
        {
          VehicleId = vehicleId,
          MediaURLContent = images
        },
        cancellationToken);
  }

  private async Task HandleLotProcessingErrorAsync(Guid lotId, string errorMessage, CancellationToken cancellationToken)
  {
    try
    {
      // Use a separate query to avoid entity tracking conflicts
      var lotToUpdate = await _tradingContext.InventAuctionLots
          .FirstOrDefaultAsync(l => l.Id == lotId, cancellationToken);

      if (lotToUpdate != null)
      {
        lotToUpdate.Status = 6;
        lotToUpdate.ImportErrorText = errorMessage;
        lotToUpdate.VehicleId = null;

        await _tradingContext.SaveChangesAsync(cancellationToken);
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Failed to update lot {LotId} with error status", lotId);
    }
  }

  private async Task FinalizeAuctionIfCompleteAsync(InventAuction auction, CancellationToken cancellationToken)
  {
    var remainingLots = await _tradingContext.InventAuctionLots
        .Where(lot => lot.AuctionId == auction.Id && lot.Status == (int)InventAuctionLotStatus.Available)
        .CountAsync(cancellationToken);

    if (remainingLots == 0)
    {
      auction.ActivityStatus = 5;
      await _tradingContext.SaveChangesAsync(cancellationToken);
    }
  }


  /// <summary>
  /// Checks if a vehicle with the given VIN or VRM already exists - simplified
  /// </summary>
  private async Task<Vehicle> FindExistingVehicleAsync(string vin, string vrm, CancellationToken cancellationToken)
  {
    // First check if there's already a lot linked to a vehicle with this VIN/VRM
    var existingLot = await _tradingContext.InventAuctionLots
        .Include(l => l.Vehicle)
        .Where(l => l.VehicleId != null &&
                   ((!string.IsNullOrEmpty(vin) && l.Vin == vin) ||
                    (!string.IsNullOrEmpty(vrm) && l.Vrm == vrm)))
        .FirstOrDefaultAsync(cancellationToken);

    if (existingLot?.Vehicle != null)
      return existingLot.Vehicle;

    // Fallback to checking vehicles table directly
    if (!string.IsNullOrEmpty(vin))
    {
      var vehicleByVin = await _tradingContext.Vehicles
          .FirstOrDefaultAsync(v => v.Vin == vin, cancellationToken);
      if (vehicleByVin != null)
        return vehicleByVin;
    }

    if (!string.IsNullOrEmpty(vrm))
    {
      var vehicleByVrm = await _tradingContext.Vehicles
          .FirstOrDefaultAsync(v => v.Vrm == vrm, cancellationToken);
      if (vehicleByVrm != null)
        return vehicleByVrm;
    }

    return null;
  }

  /// <summary>
  /// Finds an auction lot that corresponds to a vehicle - simplified
  /// </summary>
  private async Task<InventAuctionLot> FindAuctionLotByVehicleAsync(Vehicle vehicle, CancellationToken cancellationToken)
  {
    return await _tradingContext.InventAuctionLots
        .FirstOrDefaultAsync(l => l.VehicleId == vehicle.Id, cancellationToken);
  }

  /// <summary>
  /// Withdraws vehicles - simplified with direct navigation
  /// </summary>
  public async Task<bool> WithdrawVehiclesAsync(IEnumerable<Guid> ids, CancellationToken cancellationToken = default)
  {
    try
    {
      foreach (var id in ids)
      {
        var advert = await _tradingContext.Adverts
            .Include(a => a.Vehicle)
              .ThenInclude(v => v.InventAuctionLots)
            .FirstOrDefaultAsync(a => a.Id == id, cancellationToken);

        if (advert?.Vehicle == null) continue;

        advert.SoldStatus = SoldStatusEnum.Withdrawn;
        advert.AdvertStatus = AdvertStatusEnum.Ended;
        advert.EndDateTime = DateTime.Now.Subtract(TimeSpan.FromSeconds(1));
        advert.ListingEndReasonId = 2;

        // Update using direct relationship
        var lot = advert.Vehicle.InventAuctionLots?.FirstOrDefault();
        if (lot != null)
        {
          lot.Status = 5;
          lot.Updated = DateTime.Now;
        }
      }

      await _tradingContext.SaveChangesAsync(cancellationToken);
      return true;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error withdrawing vehicles");
      return false;
    }
  }

  /// <summary>
  /// Restores a vehicle - simplified with direct navigation
  /// </summary>
  public async Task<bool> RestoreVehicleAsync(Guid inventUserId, Guid lotId, CancellationToken cancellationToken = default)
  {
    try
    {
      var (auction, inventUser) = await GetAuctionAndUserDataAsync(inventUserId, cancellationToken);
      if (auction == null || inventUser == null)
      {
        return false;
      }

      var lot = await _tradingContext.InventAuctionLots.FirstOrDefaultAsync(x => x.Id == lotId);
      if (lot == null)
      {
        _logger.LogWarning($"Lot with ID {lotId} not found for restoration");
        return false;
      }

      if (lot.VehicleId == null)
      {
        _logger.LogWarning($"Lot {lotId} has no associated vehicle to restore");
        return false;
      }

      await _vehicleService.DeleteVehicle(inventUser.CustomerId, lot.VehicleId.Value, cancellationToken);
      
      lot.Status = 1;
      lot.VehicleId = null;
      lot.ImportErrorText = null;
      lot.Updated = DateTime.Now;
      
      await _tradingContext.SaveChangesAsync(cancellationToken);
      return true;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, $"Error restoring vehicle {lotId}");
      return false;
    }
  }

  /// <summary>
  /// Marks a lot as actioned - simplified
  /// </summary>
  public async Task<bool> MarkVehicleAsActionedAsync(string id, CancellationToken cancellationToken = default)
  {
    try
    {
      var vehicleId = Guid.Parse(id);
      var vehicle = await _tradingContext.Vehicles
          .Include(v => v.InventAuctionLots)
          .FirstOrDefaultAsync(v => v.Id == vehicleId, cancellationToken);

      if (vehicle == null) return false;

      var lot = vehicle.InventAuctionLots?.FirstOrDefault();
      if (lot != null)
      {
        lot.Status = 4;
        lot.Updated = DateTime.Now;
        await _tradingContext.SaveChangesAsync(cancellationToken);
        return true;
      }

      return false;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, $"Error marking vehicle {id} as actioned");
      return false;
    }
  }

  #region Helper Methods

  /// <summary>
  /// Gets a unique identifier for a lot (preferring VIN, falling back to VRM)
  /// </summary>
  private string GetLotIdentifier(InventAuctionLot lot)
  {
    if (!string.IsNullOrEmpty(lot.Vin))
      return lot.Vin;

    if (!string.IsNullOrEmpty(lot.Vrm))
      return lot.Vrm;

    return $"Lot-{lot.Id}";
  }

  private async Task<string> GetMakeNameAsync(uint? makeId, CancellationToken cancellationToken)
  {
    if (!makeId.HasValue)
      return string.Empty;

    var make = await _tradingContext.Makes
        .FirstOrDefaultAsync(m => m.Id == makeId, cancellationToken);

    return make?.MakeName ?? string.Empty;
  }

  private async Task<string> GetModelNameAsync(uint? modelId, CancellationToken cancellationToken)
  {
    if (!modelId.HasValue)
      return string.Empty;

    var model = await _tradingContext.Models
        .FirstOrDefaultAsync(m => m.Id == modelId, cancellationToken);

    return model?.ModelName ?? string.Empty;
  }

  private string GetPlaceholderImageUrl()
  {
    return URLHelper.GetImageKitURL() + "/customer/08dd0189-990b-4590-80a5-67a15ee802ea/AwaitingImage.png";
  }

  public async Task<bool> UpdateLotReservePrice(Guid id, decimal reservePrice, CancellationToken cancellationToken = default)
  {
    var lot = await _tradingContext.InventAuctionLots
      .FirstOrDefaultAsync(l => l.Id == id, cancellationToken);

    if (lot != null)
    {
      lot.ReservePrice = reservePrice;
      await _tradingContext.SaveChangesAsync(cancellationToken);  

      return true;
    }

    return false;
  }

  public async Task<List<InventUserDTO>> GetInventUsersAsync(bool isAdmin, CancellationToken cancellationToken = default)
  {
    var query = _tradingContext.InventUsers
      .AsNoTracking()
      .Select(u => new InventUserDTO
      {
        Id = u.Id,
        Added = u.Added,
        Updated = u.Updated,
        StatusId = u.StatusId,
        AddressId = u.AddressId,
        ContactId = u.ContactId,
        ContactName = u.Contact.ContactName,
        CustomerId = u.CustomerId,
        CustomerName = u.Customer.CustomerName,
        CompanyName = u.CompanyName,
        AuctionId = u.AuctionId,
        LastImportedDate = u.LastImportedDate
      });

    if (!isAdmin)
    {
      // Non-admin users can only see their own InventUser
      var userId = _userService.GetContactId();
      query = query.Where(u => u.ContactId == userId);
    }

    var users = await query
      .ToListAsync(cancellationToken);

    return users;
  }


  #endregion
}