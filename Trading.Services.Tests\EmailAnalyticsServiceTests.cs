using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.Services.Classes;
using Xunit;

namespace Trading.Services.Tests
{
  public class EmailAnalyticsServiceTests : IDisposable
  {
    private readonly TradingContext _context;
    private readonly EmailAnalyticsService _service;
    private readonly EmailConfigDTO _config;

    public EmailAnalyticsServiceTests()
    {
      // Create in-memory database for testing
      var options = new DbContextOptionsBuilder<TradingContext>()
          .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
          .Options;

      _context = new TradingContext(options);

      // Setup email configuration for testing
      _config = new EmailConfigDTO
      {
        APIKey = "test-api-key",
        Sender = "<EMAIL>",
        SenderName = "Test Sender"
      };

      var configSnapshot = new OptionsWrapper<EmailConfigDTO>(_config);
      _service = new EmailAnalyticsService(_context, configSnapshot);
    }

    [Fact]
    public async Task GetTransactionalEmailEvents_ShouldReturnEvents_WhenCalled()
    {
      // Arrange
      var startDate = DateTime.UtcNow.AddDays(-7);
      var endDate = DateTime.UtcNow;

      // Act
      var result = await _service.GetTransactionalEmailEvents(
          startDate: startDate,
          endDate: endDate,
          limit: 100
      );

      // Assert
      // Note: This test will call the actual Brevo API unless mocked
      // In a real test environment, you would mock the Brevo API calls
      Assert.NotNull(result);
    }

    [Fact]
    public async Task GetTransactionalEmailStats_ShouldReturnStats_WhenCalled()
    {
      // Arrange
      var startDate = DateTime.UtcNow.AddDays(-7);
      var endDate = DateTime.UtcNow;

      // Act
      var result = await _service.GetTransactionalEmailStats(
          startDate: startDate,
          endDate: endDate
      );

      // Assert
      // Note: This test will call the actual Brevo API unless mocked
      Assert.NotNull(result);
    }

    [Fact]
    public async Task GetDailyEmailStats_ShouldReturnDailyStats_WhenCalled()
    {
      // Arrange
      var startDate = DateTime.UtcNow.AddDays(-7);
      var endDate = DateTime.UtcNow;

      // Act
      var result = await _service.GetDailyEmailStats(
          startDate: startDate,
          endDate: endDate
      );

      // Assert
      // Note: This test will call the actual Brevo API unless mocked
      Assert.NotNull(result);
    }

    [Fact]
    public async Task GetEmailEventsForAddress_ShouldReturnEventsForSpecificEmail_WhenCalled()
    {
      // Arrange
      var emailAddress = "<EMAIL>";
      var startDate = DateTime.UtcNow.AddDays(-7);
      var endDate = DateTime.UtcNow;

      // Act
      var result = await _service.GetEmailEventsForAddress(
          emailAddress,
          startDate: startDate,
          endDate: endDate
      );

      // Assert
      // Note: This test will call the actual Brevo API unless mocked
      Assert.NotNull(result);
    }

    [Fact]
    public async Task GetTransactionalClickThroughRate_ShouldReturnCTR_WhenStatsAvailable()
    {
      // Arrange
      var startDate = DateTime.UtcNow.AddDays(-7);
      var endDate = DateTime.UtcNow;

      // Act
      var result = await _service.GetTransactionalClickThroughRate(
          startDate: startDate,
          endDate: endDate
      );

      // Assert
      // Note: This test will call the actual Brevo API unless mocked
      // The result could be null if no stats are available
      Assert.True(result == null || result >= 0);
    }

    [Fact]
    public async Task GetTransactionalEmailMetrics_ShouldReturnComprehensiveMetrics_WhenCalled()
    {
      // Arrange
      var startDate = DateTime.UtcNow.AddDays(-7);
      var endDate = DateTime.UtcNow;

      // Act
      var result = await _service.GetTransactionalEmailMetrics(
          startDate: startDate,
          endDate: endDate
      );

      // Assert
      // Note: This test will call the actual Brevo API unless mocked
      if (result != null)
      {
        Assert.Equal(startDate, result.StartDate);
        Assert.Equal(endDate, result.EndDate);
        Assert.True(result.Sent >= 0);
        Assert.True(result.Delivered >= 0);
        Assert.True(result.DeliveryRate >= 0 && result.DeliveryRate <= 1);
        Assert.True(result.OpenRate >= 0 && result.OpenRate <= 1);
        Assert.True(result.ClickThroughRate >= 0 && result.ClickThroughRate <= 1);
        Assert.True(result.BounceRate >= 0 && result.BounceRate <= 1);
      }
    }

    [Fact]
    public async Task GetEmailsForAdvert_ShouldReturnAdvertEvents_WhenCalled()
    {
      // Arrange
      var advertTag = "alert-advert-12345678-1234-1234-1234-123456789012";
      var startDate = DateTime.UtcNow.AddDays(-7);
      var endDate = DateTime.UtcNow;

      // Act
      var result = await _service.GetEmailsForAdvert(
          advertTag,
          startDate: startDate,
          endDate: endDate
      );

      // Assert
      // Note: This test will call the actual Brevo API unless mocked
      Assert.NotNull(result);
    }

    [Fact]
    public async Task StoreRecentEmailEvents_ShouldReturnZero_WhenNoEventsToStore()
    {
      // Arrange & Act
      var result = await _service.StoreRecentEmailEvents();

      // Assert
      // Note: This test will call the actual Brevo API unless mocked
      // The result should be >= 0 (number of events stored)
      Assert.True(result >= 0);
    }

    public void Dispose()
    {
      _context?.Dispose();
    }
  }
}
