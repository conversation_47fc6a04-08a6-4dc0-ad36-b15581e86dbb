using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using brevo_csharp.Api;
using brevo_csharp.Client;
using brevo_csharp.Model;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.Models;
using Trading.API.Data.Enums;
using Trading.Services.ExternalDTO;
using Trading.Services.Interfaces;
using Configuration = brevo_csharp.Client.Configuration;
using Trading.API.Data.DTO.EmailAnalytics;

namespace Trading.Services.Classes;

public class EmailAnalyticsService : IEmailAnalyticsService
{
  private readonly TradingContext _tradingContext;
  private readonly EmailConfigDTO _config;

  public EmailAnalyticsService(
      TradingContext tradingContext,
      IOptionsSnapshot<EmailConfigDTO> configuration)
  {
    _tradingContext = tradingContext;
    _config = configuration.Value;

    // Configure Brevo API
    Configuration.Default.ApiKey.Clear();
    Configuration.Default.ApiKey["api-key"] = _config.APIKey;
  }

  /// <summary>
  /// Get transactional email events (opens, clicks, deliveries, etc.)
  /// </summary>
  /// <param name="startDate">Start date for the report</param>
  /// <param name="endDate">End date for the report</param>
  /// <param name="limit">Number of records to fetch (max 2500)</param>
  /// <param name="offset">Offset for pagination</param>
  /// <param name="email">Filter by specific email address</param>
  /// <param name="templateId">Filter by template ID</param>
  /// <param name="messageId">Filter by specific message ID</param>
  /// <param name="event">Filter by event type (sent, delivery, bounce, request, opened, click, invalid, deferred, blocked)</param>
  /// <returns>Email event report</returns>
  public async Task<GetEmailEventReport> GetTransactionalEmailEvents(
      DateTime? startDate = null,
      DateTime? endDate = null,
      long? limit = 2500,
      long? offset = 0,
      string email = null,
      long? templateId = null,
      string messageId = null,
      string eventType = null)
  {
    try
    {
      var apiInstance = new TransactionalEmailsApi();

      // Default to last 30 days if no dates provided
      if (!startDate.HasValue && !endDate.HasValue)
      {
        endDate = DateTime.UtcNow;
        startDate = endDate.Value.AddDays(-30);
      }

      var result = await apiInstance.GetEmailEventReportAsync(
          limit: limit,
          offset: offset,
          startDate: startDate?.ToString("yyyy-MM-dd"),
          endDate: endDate?.ToString("yyyy-MM-dd"),
          email: email,
          templateId: templateId,
          messageId: messageId,
          _event: eventType
      );

      return result;
    }
    catch (Exception e)
    {
      Debug.WriteLine($"Error getting email events: {e.Message}");
      return null;
    }
  }

  /// <summary>
  /// Get aggregated transactional email statistics
  /// </summary>
  /// <param name="startDate">Start date</param>
  /// <param name="endDate">End date</param>
  /// <param name="days">Number of days (alternative to date range)</param>
  /// <param name="tag">Filter by tag</param>
  /// <returns>Aggregated email statistics</returns>
  public async Task<GetAggregatedReport> GetTransactionalEmailStats(
      DateTime? startDate = null,
      DateTime? endDate = null,
      long? days = null,
      string tag = null)
  {
    try
    {
      var apiInstance = new TransactionalEmailsApi();

      var result = await apiInstance.GetAggregatedSmtpReportAsync(
          startDate: startDate?.ToString("yyyy-MM-dd"),
          endDate: endDate?.ToString("yyyy-MM-dd"),
          days: days,
          tag: tag
      );

      return result;
    }
    catch (Exception e)
    {
      Debug.WriteLine($"Error getting aggregated email stats: {e.Message}");
      return null;
    }
  }

  /// <summary>
  /// Get detailed statistics for emails by day
  /// </summary>
  /// <param name="startDate">Start date</param>
  /// <param name="endDate">End date</param>
  /// <param name="days">Number of days</param>
  /// <param name="tag">Filter by tag</param>
  /// <returns>Daily email statistics</returns>
  public async Task<GetReports> GetDailyEmailStats(
      DateTime? startDate = null,
      DateTime? endDate = null,
      long? days = null,
      string tag = null)
  {
    try
    {
      var apiInstance = new TransactionalEmailsApi();

      var result = await apiInstance.GetSmtpReportAsync(
          startDate: startDate?.ToString("yyyy-MM-dd"),
          endDate: endDate?.ToString("yyyy-MM-dd"),
          days: days,
          tag: tag
      );

      return result;
    }
    catch (Exception e)
    {
      Debug.WriteLine($"Error getting daily email stats: {e.Message}");
      return null;
    }
  }

  /// <summary>
  /// Get email events for a specific email address
  /// </summary>
  /// <param name="emailAddress">The email address to search for</param>
  /// <param name="startDate">Start date</param>
  /// <param name="endDate">End date</param>
  /// <param name="limit">Number of records</param>
  /// <returns>Email events for the specified address</returns>
  public async Task<GetEmailEventReport> GetEmailEventsForAddress(
      string emailAddress,
      DateTime? startDate = null,
      DateTime? endDate = null,
      long? limit = 100)
  {
    return await GetTransactionalEmailEvents(
        startDate: startDate,
        endDate: endDate,
        limit: limit,
        email: emailAddress
    );
  }

  /// <summary>
  /// Get click-through rate for a specific period
  /// </summary>
  /// <param name="startDate">Start date</param>
  /// <param name="endDate">End date</param>
  /// <param name="tag">Optional tag filter</param>
  /// <returns>Click-through rate as decimal</returns>
  public async Task<decimal?> GetTransactionalClickThroughRate(
      DateTime? startDate = null,
      DateTime? endDate = null,
      string tag = null)
  {
    try
    {
      var stats = await GetTransactionalEmailStats(startDate, endDate, null, tag);

      if (stats != null && stats.Delivered > 0 && stats.UniqueClicks.HasValue)
      {
        return (decimal)stats.UniqueClicks.Value / (decimal)stats.Delivered.Value;
      }
    }
    catch (Exception e)
    {
      Debug.WriteLine($"Error calculating CTR: {e.Message}");
    }

    return null;
  }

  /// <summary>
  /// Get comprehensive email analytics for a period
  /// </summary>
  /// <param name="startDate">Start date</param>
  /// <param name="endDate">End date</param>
  /// <param name="tag">Optional tag filter</param>
  /// <returns>Comprehensive email metrics</returns>
  public async Task<TransactionalEmailMetricsDTO> GetTransactionalEmailMetrics(
      DateTime? startDate = null,
      DateTime? endDate = null,
      string tag = null)
  {
    try
    {
      var stats = await GetTransactionalEmailStats(startDate, endDate, null, tag);

      if (stats == null)
        return null;

      var delivered = stats.Delivered ?? 0;
      var uniqueOpens = stats.UniqueOpens ?? 0;
      var uniqueClicks = stats.UniqueClicks ?? 0;
      var hardBounces = stats.HardBounces ?? 0;
      var softBounces = stats.SoftBounces ?? 0;
      var bounces = hardBounces + softBounces;

      // Calculate sent as delivered + bounces + blocked + invalid (approximation)
      var sent = delivered + bounces + (stats.Blocked ?? 0) + (stats.Invalid ?? 0);

      return new TransactionalEmailMetricsDTO
      {
        StartDate = startDate,
        EndDate = endDate,
        Tag = tag,

        // Counts (using long to match API)
        Sent = sent,
        Delivered = delivered,
        Opens = stats.Opens ?? 0,
        UniqueOpens = uniqueOpens,
        Clicks = stats.Clicks ?? 0,
        UniqueClicks = uniqueClicks,
        Bounces = bounces,
        HardBounces = hardBounces,
        SoftBounces = softBounces,
        Blocked = stats.Blocked ?? 0,
        Invalid = stats.Invalid ?? 0,
        Unsubscribed = stats.Unsubscribed ?? 0,

        // Rates
        DeliveryRate = sent > 0 ? (decimal)delivered / (decimal)sent : 0,
        OpenRate = delivered > 0 ? (decimal)uniqueOpens / (decimal)delivered : 0,
        ClickThroughRate = delivered > 0 ? (decimal)uniqueClicks / (decimal)delivered : 0,
        BounceRate = sent > 0 ? (decimal)bounces / (decimal)sent : 0
      };
    }
    catch (Exception e)
    {
      Debug.WriteLine($"Error getting email metrics: {e.Message}");
      return null;
    }
  }

  /// <summary>
  /// Get emails sent for a specific advert/tag
  /// </summary>
  /// <param name="advertTag">The tag identifying the advert</param>
  /// <param name="startDate">Start date</param>
  /// <param name="endDate">End date</param>
  /// <returns>Email events for the advert</returns>
  public async Task<GetEmailEventReport> GetEmailsForAdvert(
      string advertTag,
      DateTime? startDate = null,
      DateTime? endDate = null)
  {
    // Note: This assumes you're using tags to identify adverts when sending emails
    // You might need to adjust based on how you identify advert-related emails
    return await GetTransactionalEmailEvents(
        startDate: startDate,
        endDate: endDate,
        limit: 2500
    );
  }

  /// <summary>
  /// Fetch email events from the past 48 hours and store new ones in the AdvertEvent table
  /// </summary>
  /// <returns>Number of new events stored</returns>
  public async Task<int> StoreRecentEmailEvents()
  {
    try
    {
      // Get events from the past 48 hours
      var endDate = DateTime.UtcNow;
      var startDate = endDate.AddHours(-48);
      var eventReport = await GetTransactionalEmailEvents(
          startDate: startDate,
          endDate: endDate,
          limit: 2500
      );
      if (eventReport?.Events == null || !eventReport.Events.Any())
      {
        return 0;
      }

      // Define meaningful events to track
      var meaningfulEvents = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
       {
         "delivered",
         "opened",
         "click",
         "clicks",
         "bounced",
         "blocked",
         "spam",
         "unsubscribed"
       };

      var newEventsCount = 0;
      foreach (var emailEvent in eventReport.Events)
      {
        // Skip events that don't have a tag or don't match the advert pattern
        if (string.IsNullOrEmpty(emailEvent.Tag) ||
            !emailEvent.Tag.StartsWith("alert-advert-", StringComparison.OrdinalIgnoreCase))
        {
          continue;
        }

        // Filter for meaningful events only
        if (!meaningfulEvents.Contains(emailEvent.Event.ToString()))
        {
          continue;
        }

        // Check if this event already exists to avoid duplicates
        var existingEvent = await _tradingContext.AdvertEvents
            .FirstOrDefaultAsync(ae =>
                ae.MessageId == emailEvent.MessageId &&
                ae.Event == emailEvent.Event.ToString() &&
                ae.Email == emailEvent.Email);
        if (existingEvent != null)
        {
          continue; // Skip duplicate
        }

        // Extract advert ID from tag (format: alert-advert-{guid})
        Guid? advertId = null;
        var match = Regex.Match(emailEvent.Tag, @"alert-advert-([a-fA-F0-9\-]{36})", RegexOptions.IgnoreCase);
        if (match.Success && Guid.TryParse(match.Groups[1].Value, out var parsedAdvertId))
        {
          advertId = parsedAdvertId;
        }

        // If no valid advert ID found, skip this event
        if (advertId == null)
        {
          continue;
        }

        // Create new AdvertEvent
        var advertEvent = new AdvertEvent
        {
          Event = emailEvent.Event.ToString(),
          Email = emailEvent.Email,
          Date = DateTime.TryParse(emailEvent.Date, out var parsedDate) ? parsedDate : DateTime.Now,
          MessageId = emailEvent.MessageId,
          Tag = emailEvent.Tag,
          Ip = emailEvent.Ip,
          From = emailEvent.From,
          AdvertId = advertId,
          StatusId = (uint)StatusEnum.Active,
          Added = DateTime.Now,
          Updated = DateTime.Now
        };

        _tradingContext.AdvertEvents.Add(advertEvent);
        newEventsCount++;
      }

      if (newEventsCount > 0)
      {
        await _tradingContext.SaveChangesAsync();
      }

      return newEventsCount;
    }
    catch (Exception ex)
    {
      // log the exception
      throw;
    }
  }


  // Contact Issues Management


  /// <summary>
  /// Get all contacts with email delivery issues using Brevo's dedicated blocked contacts endpoint
  /// </summary>
  /// <param name="startDate">Start date for the search (optional)</param>
  /// <param name="endDate">End date for the search (optional)</param>
  /// <param name="limit">Number of records to fetch per page (max 50)</param>
  /// <param name="offset">Offset for pagination</param>
  /// <returns>List of blocked/unsubscribed/spam contacts</returns>
  public async Task<List<ContactIssueDTO>> GetContactsWithIssues(
      DateTime? startDate = null,
      DateTime? endDate = null,
      long? limit = 100,
      long? offset = 0)
  {
    try
    {
      var apiInstance = new TransactionalEmailsApi();
      var contactIssues = new List<ContactIssueDTO>();

      var result = await apiInstance.GetTransacBlockedContactsAsync(
          startDate: startDate?.ToString("yyyy-MM-dd"),
          endDate: endDate?.ToString("yyyy-MM-dd"),
          limit: limit,
          offset: offset
      );

      if (result?.Contacts != null)
      {
        foreach (var contact in result.Contacts)
        {
          contactIssues.Add(new ContactIssueDTO
          {
            Email = contact.Email,
            IssueDescription = contact.Reason.Message, // This will be "unsubscribed", "blocked", "hardBounce", "spam", etc.
            IssueType = contact.Reason.Code.ToString().ToLowerInvariant(), // Convert to lowercase for consistency
            IssueDate = DateTime.TryParse(contact.BlockedAt, out var date) ? date : DateTime.Now,
            Sender = contact.SenderEmail
          });
        }
      }

      return contactIssues.OrderBy(c => c.Email).ToList();
    }
    catch (Exception ex)
    {
      throw new Exception($"Error retrieving contacts with issues: {ex.Message}", ex);
    }
  }

  /// <summary>
  /// Get contacts filtered by issue type
  /// </summary>
  /// <param name="issueType">Filter by specific issue type (unsubscribed, blocked, hardBounce, etc.)</param>
  /// <param name="startDate">Start date for the search</param>
  /// <param name="endDate">End date for the search</param>
  /// <returns>Filtered list of contacts</returns>
  public async Task<List<ContactIssueDTO>> GetContactsByIssueType(
      string issueType,
      DateTime? startDate = null,
      DateTime? endDate = null)
  {
    var allIssues = await GetContactsWithIssues(startDate, endDate);

    if (string.IsNullOrEmpty(issueType))
      return allIssues;

    return allIssues
        .Where(i => i.IssueType.Contains(issueType, StringComparison.OrdinalIgnoreCase))
        .ToList();
  }

  /// <summary>
  /// Generate a summary report of contact issues
  /// </summary>
  /// <param name="startDate">Start date for the search</param>
  /// <param name="endDate">End date for the search</param>
  /// <returns>Summary statistics</returns>
  public async Task<ContactIssuesSummaryDTO> GetContactIssuesSummary(
      DateTime? startDate = null,
      DateTime? endDate = null)
  {
    try
    {
      var allIssues = await GetContactsWithIssues(startDate, endDate);

      return new ContactIssuesSummaryDTO
      {
        TotalContacts = allIssues.Count,
        UnsubscribedCount = allIssues.Count(i => i.IssueType.Contains("unsubscribed", StringComparison.OrdinalIgnoreCase)),
        BlockedCount = allIssues.Count(i => i.IssueType.Contains("blocked", StringComparison.OrdinalIgnoreCase)),
        HardBounceCount = allIssues.Count(i => i.IssueType.Contains("hardBounce", StringComparison.OrdinalIgnoreCase)),
        SpamCount = allIssues.Count(i => i.IssueType.Contains("spam", StringComparison.OrdinalIgnoreCase)),
        GeneratedDate = DateTime.Now,
        SearchPeriod = startDate.HasValue && endDate.HasValue
              ? $"{startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}"
              : "All time"
      };
    }
    catch (Exception ex)
    {
      throw new Exception($"Error generating contact issues summary: {ex.Message}", ex);
    }
  }

}
