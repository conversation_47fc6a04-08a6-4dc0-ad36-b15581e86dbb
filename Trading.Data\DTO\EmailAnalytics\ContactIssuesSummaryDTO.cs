﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.EmailAnalytics;

public class ContactIssuesSummaryDTO
{
  public int TotalContacts { get; set; }
  public int UnsubscribedCount { get; set; }
  public int BlockedCount { get; set; }
  public int HardBounceCount { get; set; }
  public int SpamCount { get; set; }
  public DateTime GeneratedDate { get; set; }
  public string SearchPeriod { get; set; }
}
