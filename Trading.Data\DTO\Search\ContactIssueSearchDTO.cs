using System;

namespace Trading.API.Data.DTO.Search
{
  public class ContactIssueSearchDTO : BaseSearchDTO
  {
    public ContactIssueSearchFiltersDTO Filters { get; set; } = new ContactIssueSearchFiltersDTO();
  }

  public class ContactIssueSearchFiltersDTO
  {
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string IssueType { get; set; } // Filter by specific issue type (unsubscribed, blocked, hardBounce, spam, etc.)
  }
}
