﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Trading.API.Data.Models
{
  [Table("AdvertEvent")]
  public class AdvertEvent : BaseModelEntityInt
  {
    /// <summary>
    /// The type of email event (e.g., "loadedByProxy", "opened", "clicked", etc.)
    /// </summary>
    [MaxLength(24)]
    public string Event { get; set; }

    /// <summary>
    /// Email address associated with the event
    /// </summary>
    [MaxLength(255)]
    public string Email { get; set; }

    /// <summary>
    /// Date and time when the event occurred
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// Unique message identifier from the email service
    /// </summary>
    [MaxLength(255)]
    public string MessageId { get; set; }

    /// <summary>
    /// Tag associated with the email (format: alert-advert-{advertId})
    /// </summary>
    [MaxLength(100)]
    public string Tag { get; set; }

    /// <summary>
    /// IP address from which the event originated
    /// </summary>
    [MaxLength(45)]
    public string Ip { get; set; }

    /// <summary>
    /// From email address
    /// </summary>
    [MaxLength(255)]
    public string From { get; set; }

    /// <summary>
    /// Foreign key to the Advert table (extracted from tag)
    /// </summary>
    public Guid? AdvertId { get; set; }

    /// <summary>
    /// Navigation property to the related Advert
    /// </summary>
    [ForeignKey("AdvertId")]
    public virtual Advert Advert { get; set; }
  }
}
