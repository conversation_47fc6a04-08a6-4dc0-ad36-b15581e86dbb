﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Admin;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.Services.Helpers;
using Trading.Services.Interfaces;

namespace Trading.Services.Classes;

public class AdminSearchStatsService : IAdminSearchStatsService
{
  private readonly TradingContext _tradingContext;
  private readonly IMapper _mapper;
  private readonly IAdvertSearchRepository _advertSearchRepository;

  public AdminSearchStatsService(TradingContext tradingContext, IMapper mapper, IAdvertSearchRepository advertSearchRepository)
  {
    _tradingContext = tradingContext;
    _mapper = mapper;
    _advertSearchRepository = advertSearchRepository;
  }

  public async Task<SearchResultDTO<AdminCustomerSearchAnalyticsDTO>> GetCustomerSearchAnalytics(
      AdminCustomerSearchAnalyticsRequestDTO request, CancellationToken cancellationToken)
  {
    var fromDate = request.Filters?.FromDate ?? DateTime.Now.AddDays(-30);

    // Build base query for saved searches with all necessary includes
    var savedSearchQuery = _tradingContext.SavedSearches
        .Include(x => x.Contact)
            .ThenInclude(x => x.Customer)
                .ThenInclude(x => x.CustomerInternalInfo)
        .Include(x => x.Search)
        .AsNoTracking()
        .Where(x => x.StatusId == (int)StatusEnum.Active)
        .AsQueryable();

    // Apply filters
    if (request.Filters != null)
    {
      if (!string.IsNullOrEmpty(request.Filters.CustomerName))
      {
        savedSearchQuery = savedSearchQuery.Where(x => x.Contact.Customer.CustomerName.Contains(request.Filters.CustomerName));
      }

      if (request.Filters.AssignedTo.HasValue)
      {
        savedSearchQuery = savedSearchQuery.Where(x => x.Contact.Customer.CustomerInternalInfo.AssignedTo == request.Filters.AssignedTo.Value);
      }

      if (request.Filters.HasActiveNotifications.HasValue)
      {
        savedSearchQuery = savedSearchQuery.Where(x => x.SendUpdates == request.Filters.HasActiveNotifications.Value);
      }

      if (request.Filters.FromDate.HasValue)
      {
        savedSearchQuery = savedSearchQuery.Where(x => x.Added >= request.Filters.FromDate.Value);
      }
    }

    var allSavedSearches = await savedSearchQuery.ToListAsync(cancellationToken);

    if (!allSavedSearches.Any())
    {
      return new SearchResultDTO<AdminCustomerSearchAnalyticsDTO>
      {
        Results = new List<AdminCustomerSearchAnalyticsDTO>(),
        TotalItems = 0
      };
    }

    // Get notification statistics using the new relationship - MUCH FASTER!
    var savedSearchIds = allSavedSearches.Select(x => x.Id).ToList();
    var notificationStats = await GetLinkedNotificationStats(savedSearchIds, fromDate, cancellationToken);
    var matchData = await GetMatchDataForSavedSearches(savedSearchIds, fromDate, cancellationToken);

    // Group by customer and build results
    var customerGroups = allSavedSearches
        .GroupBy(ss => new {
          CustomerId = ss.Contact.Customer.Id,
          CustomerName = ss.Contact.Customer.CustomerName,
          CustomerEmail = ss.Contact.Customer.Email,
          AssignedTo = ss.Contact.Customer.CustomerInternalInfo?.AssignedTo
        })
        .Select(g => {
          var customerSearches = g.ToList();
          var customerSearchIds = customerSearches.Select(cs => cs.Id).ToList();
          var customerNotificationStats = notificationStats.Where(ns => customerSearchIds.Contains(ns.SavedSearchId)).ToList();
          var customerMatchData = matchData.Where(md => customerSearchIds.Contains(md.SavedSearchId)).ToList();

          return new AdminCustomerSearchAnalyticsDTO
          {
            CustomerId = g.Key.CustomerId,
            CustomerName = g.Key.CustomerName,
            CustomerEmail = g.Key.CustomerEmail,
            AssignedTo = g.Key.AssignedTo,
            TotalSavedSearches = customerSearches.Count,
            ActiveNotificationSearches = customerSearches.Count(ss => ss.SendUpdates == true),
            TotalMatchingAdverts = customerMatchData.Sum(md => md.MatchCount),
            TotalAdvertsNotifiedAbout = customerNotificationStats.Sum(ns => ns.DistinctAdvertsNotifiedAbout),
            SavedSearches = customerSearches.Select(ss => {
              var searchNotificationStats = notificationStats.FirstOrDefault(ns => ns.SavedSearchId == ss.Id);
              var searchMatchData = matchData.FirstOrDefault(md => md.SavedSearchId == ss.Id);

              return new SavedSearchStatsDTO
              {
                Id = ss.Id,
                SearchName = ss.SearchName,
                ContactEmail = ss.Contact.Email,
                ContactName = ss.Contact.ContactName,
                SearchPhrase = ss.Search?.SearchPhrase,
                SearchDescription = ss.Search?.Description,
                SendUpdates = ss.SendUpdates,
                UpdateFrequency = ss.UpdateFrequency,
                IsProfile = ss.IsProfile,
                Added = ss.Added,
                NotificationTime = ss.NotificationTime,
                PauseUntil = ss.PauseUntil,
                IsPaused = ss.PauseUntil.HasValue && ss.PauseUntil > DateTime.Now,
                MatchingAdvertCount = searchMatchData?.MatchCount ?? 0,
                AdvertsNotifiedAbout = searchNotificationStats?.DistinctAdvertsNotifiedAbout ?? 0,
                MatchingAdverts = searchMatchData?.SimplifiedAdverts ?? new List<SimplifiedAdvertReferenceDTO>()
              };
            }).OrderByDescending(ss => ss.Added).ToList()
          };
        })
        .ToList();

    // Apply customer-level filtering
    if (request.Filters?.MaxSavedSearches.HasValue == true)
    {
      customerGroups = customerGroups.Where(x => x.TotalSavedSearches < request.Filters.MaxSavedSearches.Value).ToList();
    }

    // Apply ordering
    customerGroups = ApplyCustomerOrdering(customerGroups, request.Order);

    var totalCount = customerGroups.Count;

    // Apply pagination
    if (request.Offset.HasValue)
      customerGroups = customerGroups.Skip(request.Offset.Value).ToList();

    if (request.Limit.HasValue)
      customerGroups = customerGroups.Take(request.Limit.Value).ToList();

    return new SearchResultDTO<AdminCustomerSearchAnalyticsDTO>
    {
      Results = customerGroups,
      TotalItems = totalCount
    };
  }

  public async Task<SearchResultDTO<AdvertNotificationStatsDTO>> GetAdvertNotificationAnalytics(
      AdminAdvertAnalyticsRequestDTO request, CancellationToken cancellationToken)
  {
    var fromDate = request.Filters?.FromDate ?? DateTime.Now.AddDays(-30);
    var toDate = request.Filters?.ToDate ?? DateTime.Now.AddSeconds(10);

    // Build base advert query
    var advertQuery = _tradingContext.Adverts
        .Include(x => x.Customer)
        .Include(x => x.Vehicle.Make)
        .Include(x => x.Vehicle.Model)
        .Include(x => x.Vehicle.Deriv)
        .Include(x => x.Status)
        .AsNoTracking()
        .Where(x => x.Updated >= fromDate && x.Updated <= toDate)
        .AsQueryable();

    // Apply filters
    if (request.Filters != null)
    {
      if (request.Filters.AdvertId.HasValue)
      {
        advertQuery = advertQuery.Where(x => x.Id == request.Filters.AdvertId.Value);
      }

      if (!string.IsNullOrEmpty(request.Filters.Vrm))
      {
        advertQuery = advertQuery.Where(x => x.Vehicle.Vrm.Contains(request.Filters.Vrm));
      }

      if (request.Filters.AdvertStatus.HasValue)
      {
        advertQuery = advertQuery.Where(x => x.AdvertStatus == request.Filters.AdvertStatus.Value);
      }

      if (request.Filters.SoldStatus.HasValue)
      {
        advertQuery = advertQuery.Where(x => x.SoldStatus == request.Filters.SoldStatus.Value);
      }

      if (request.Filters.StatusId.HasValue)
      {
        advertQuery = advertQuery.Where(x => x.StatusId == request.Filters.StatusId.Value);
      }

      if (request.Filters.VendorId.HasValue)
      {
        advertQuery = advertQuery.Where(x => x.CustomerId == request.Filters.VendorId.Value);
      }
    }

    var totalCount = await advertQuery.CountAsync(cancellationToken);

    // Apply ordering
    advertQuery = ApplyAdvertOrdering(advertQuery, request.Order);

    // Apply pagination before processing expensive calculations
    if (request.Offset.HasValue)
      advertQuery = advertQuery.Skip(request.Offset.Value);

    if (request.Limit.HasValue)
      advertQuery = advertQuery.Take(request.Limit.Value);

    var adverts = await advertQuery.ToListAsync(cancellationToken);

    // Get notification statistics using the new relationship - MUCH FASTER!
    var advertIds = adverts.Select(x => x.Id).ToList();
    var notificationStats = await GetLinkedNotificationStatsForAdverts(advertIds, fromDate, cancellationToken);

    // Use reverse search approach for search matching - more efficient
    List<AdvertSearchMatchStats> searchMatchStats = null;
    if (request.Filters?.AdvertId.HasValue == true)
    {
      // Only do detailed search matching for single advert lookups
      searchMatchStats = await GetReverseSearchMatchStatsForAdverts(advertIds, fromDate, cancellationToken);
    }
    else
    {
      // For list views, use the notification data to estimate search matches
      searchMatchStats = await GetEstimatedSearchMatchStatsFromNotifications(advertIds, fromDate, cancellationToken);
    }

    var results = adverts.Select(advert => {
      var matchStats = searchMatchStats?.FirstOrDefault(sms => sms.AdvertId == advert.Id);
      var notifStats = notificationStats.FirstOrDefault(ns => ns.AdvertId == advert.Id);

      return new AdvertNotificationStatsDTO
      {
        AdvertId = advert.Id,
        Vrm = advert.Vehicle?.Vrm,
        CustomerId = advert.CustomerId,
        CustomerName = advert.Customer.CustomerName,
        Description = advert.Vehicle?.Make?.MakeName + " " + advert.Vehicle?.Model?.ModelName + " " + advert.Vehicle?.Deriv?.DerivName + " (" + advert.Vehicle?.Odometer + " miles)",
        Added = advert.Added,
        Updated = advert.Updated,
        AdvertStatus = advert.AdvertStatus,
        SoldStatus = advert.SoldStatus,
        StatusId = advert.StatusId,
        StatusName = advert.Status?.StatusName,
        MatchedInSavedSearchCount = matchStats?.SavedSearchMatches ?? 0,
        MatchedInUnsavedSearchCount = matchStats?.UnsavedSearchMatches ?? 0,
        ContactsNotifiedAbout = notifStats?.ContactsNotifiedAbout ?? 0
      };
    }).ToList();

    // Apply post-processing filters that require calculated data
    if (request.Filters != null)
    {
      if (request.Filters.MaxMatchedSavedSearches.HasValue)
      {
        results = results.Where(x => x.MatchedInSavedSearchCount < request.Filters.MaxMatchedSavedSearches.Value).ToList();
      }

      if (request.Filters.MaxNotifications.HasValue)
      {
        results = results.Where(x => x.ContactsNotifiedAbout < request.Filters.MaxNotifications.Value).ToList();
      }

      if (request.Filters.MaxNotificationRate.HasValue)
      {
        results = results.Where(x => x.NotificationEffectivenessRate < request.Filters.MaxNotificationRate.Value).ToList();
      }
    }

    return new SearchResultDTO<AdvertNotificationStatsDTO>
    {
      Results = results,
      TotalItems = totalCount
    };
  }

  public async Task<AdminSearchSystemOverviewDTO> GetSearchSystemOverview(
      DateTime? fromDate, CancellationToken cancellationToken)
  {
    var reportStart = fromDate ?? DateTime.Now.AddDays(-30);

    // Basic counts
    var totalCustomers = await _tradingContext.Customers
      .AsNoTracking()
      .Where(x => x.StatusId == (uint)StatusEnum.Active)
      .CountAsync(cancellationToken);

    var customersWithSearches = await _tradingContext.Customers
        .Where(x => x.StatusId == (uint)StatusEnum.Active && x.Contacts.Any(c => c.SavedSearches.Any(ss => ss.StatusId == (int)StatusEnum.Active)))
        .CountAsync(cancellationToken);

    var totalContacts = await _tradingContext.Contacts
      .Where(x => x.StatusId == (uint)StatusEnum.Active)
      .CountAsync(cancellationToken);

    var customersWithNoSearches = await _tradingContext.Customers
        .Where(x => x.StatusId == (uint)StatusEnum.Active && !x.Contacts.Any(c => c.SavedSearches.Any(ss => ss.StatusId == (int)StatusEnum.Active)))
        .CountAsync(cancellationToken);

    var totalSavedSearches = await _tradingContext.SavedSearches
        .Where(x => x.StatusId == (int)StatusEnum.Active)
        .CountAsync(cancellationToken);

    var activeSavedSearches = await _tradingContext.SavedSearches
        .Where(x => x.StatusId == (int)StatusEnum.Active && x.SendUpdates == true)
        .CountAsync(cancellationToken);

    var totalSearches = await _tradingContext.Searches
      .Where(x => x.StatusId == (uint)StatusEnum.Active)
      .CountAsync(cancellationToken);

    var orphanedSearches = await _tradingContext.Searches
        .Where(x => !x.SavedSearches.Any())
        .CountAsync(cancellationToken);

    // Advert metrics
    var totalActiveAdverts = await _tradingContext.Adverts
        .Where(x => x.StatusId == (int)StatusEnum.Active && 
          x.AdvertStatus == AdvertStatusEnum.Active && x.SoldStatus == SoldStatusEnum.Active && x.Updated >= reportStart)
        .CountAsync(cancellationToken);

    var advertsWithNotifications = await _tradingContext.Adverts
        .Where(x => x.StatusId == (int)StatusEnum.Active && 
                   x.Updated >= reportStart && x.AdvertStatus == AdvertStatusEnum.Active && x.SoldStatus == SoldStatusEnum.Active &&
                   x.Notifications.Any(n => n.Added >= reportStart))
        .CountAsync(cancellationToken);

    // Get notification metrics using new relationship - MUCH FASTER!
    var notificationMetrics = await CalculateLinkedSystemNotificationMetrics(reportStart, cancellationToken);

    // Build key metrics for dashboard
    var keyMetrics = new List<SystemMetricSummaryDTO>
      {
          new SystemMetricSummaryDTO
          {
              Name = "Customer Engagement",
              Value = $"{Math.Round((decimal)customersWithSearches / Math.Max(totalCustomers, 1) * 100, 1):F1}%",
              Description = "Percentage of customers with active saved searches",
              TrendIndicator = "Unknown"
          },
          new SystemMetricSummaryDTO
          {
              Name = "Search Effectiveness",
              Value = $"{Math.Round((decimal)notificationMetrics.TotalAdvertsNotifiedAbout / Math.Max(totalActiveAdverts, 1), 2):F2}",
              Description = "Average adverts notified about per active advert",
              TrendIndicator = "Unknown"
          },
          new SystemMetricSummaryDTO
          {
              Name = "Notification Coverage",
              Value = $"{Math.Round((decimal)notificationMetrics.LinkedNotificationPercentage, 1):F1}%",
              Description = "Percentage of notifications properly linked to saved searches",
              TrendIndicator = "Unknown"
          },
          new SystemMetricSummaryDTO
          {
              Name = "System Health",
              Value = activeSavedSearches > 0 && advertsWithNotifications > 0 ? "Operational" : "Needs Attention",
              Description = "Overall system operational status",
              TrendIndicator = "Unknown"
          },
          new SystemMetricSummaryDTO
          {
              Name = "Customers with Saved Searches",
              Value = customersWithSearches.ToString(),
              Description = "Number of customers with active saved searches",
              TrendIndicator = "Unknown"
          },
          new SystemMetricSummaryDTO
          {
              Name = "Total Contacts",
              Value = totalContacts.ToString(),
              Description = "Total number of active contacts in the system",
              TrendIndicator = "Unknown"
          },
          new SystemMetricSummaryDTO
          {
              Name = "Total Searches",
              Value = totalSearches.ToString(),
              Description = "Total number of active searches in the system",
              TrendIndicator = "Unknown"
          },
          new SystemMetricSummaryDTO
          {
              Name = "Orphaned Searches",
              Value = orphanedSearches.ToString(),
              Description = "Number of searches not linked to any saved search",
              TrendIndicator = orphanedSearches > 0 ? "Warning" : "Good"
          },
          new SystemMetricSummaryDTO
          {
              Name = "Adverts with Saved Search Matches",
              Value = notificationMetrics.AdvertsWithSavedSearchMatches.ToString(),
              Description = "Number of adverts that matched saved searches",
              TrendIndicator = "Unknown"
          },
          new SystemMetricSummaryDTO
          {
              Name = "Adverts with Unsaved Search Matches",
              Value = notificationMetrics.AdvertsWithUnsavedSearchMatches.ToString(),
              Description = "Number of adverts that matched unsaved searches",
              TrendIndicator = "Unknown"
          },
          new SystemMetricSummaryDTO
          {
              Name = "Total Contacts Notified",
              Value = notificationMetrics.TotalContactsNotified.ToString(),
              Description = "Total number of contacts that received notifications",
              TrendIndicator = "Unknown"
          }
      };

    return new AdminSearchSystemOverviewDTO
    {
      TotalCustomers = totalCustomers,
      CustomersWithSavedSearches = customersWithSearches,
      AdvertsWithSavedSearchMatches = notificationMetrics.AdvertsWithSavedSearchMatches,
      AdvertsWithUnsavedSearchMatches = notificationMetrics.AdvertsWithUnsavedSearchMatches,
      TotalContacts = totalContacts,
      CustomersWithNoSavedSearches = totalCustomers - customersWithSearches,
      OrphanedSearches = orphanedSearches,
      TotalContactsNotified = notificationMetrics.TotalContactsNotified,
      TotalSearches = totalSearches,
      CustomersWithNoSearches = customersWithNoSearches,
      TotalSavedSearches = totalSavedSearches,
      ActiveSavedSearches = activeSavedSearches,
      TotalActiveAdverts = totalActiveAdverts,
      AdvertsWithNotifications = advertsWithNotifications,
      TotalAdvertsNotifiedAbout = notificationMetrics.TotalAdvertsNotifiedAbout,
      ReportPeriodStart = reportStart,
      ReportGenerated = DateTime.Now,
      KeyMetrics = keyMetrics
    };
  }

  // Private helper methods - NOW MUCH FASTER WITH LINKED NOTIFICATIONS!
  private async Task<List<SavedSearchNotificationStats>> GetLinkedNotificationStats(
      List<Guid> savedSearchIds, DateTime fromDate, CancellationToken cancellationToken)
  {
    // This is now a simple, fast query thanks to the SavedSearchId relationship!
    var stats = await _tradingContext.Notifications
        .Where(x => x.SavedSearchId.HasValue &&
                   savedSearchIds.Contains(x.SavedSearchId.Value) &&
                   x.Added >= fromDate)
        .GroupBy(x => x.SavedSearchId.Value)
        .Select(g => new SavedSearchNotificationStats
        {
          SavedSearchId = g.Key,
          DistinctAdvertsNotifiedAbout = g.Select(n => n.AdvertId).Distinct().Count(),
          TotalNotifications = g.Count()
        })
        .AsNoTracking()
        .ToListAsync(cancellationToken);

    // Add entries for saved searches with no notifications
    var missingSearchIds = savedSearchIds.Except(stats.Select(s => s.SavedSearchId)).ToList();
    stats.AddRange(missingSearchIds.Select(id => new SavedSearchNotificationStats
    {
      SavedSearchId = id,
      DistinctAdvertsNotifiedAbout = 0,
      TotalNotifications = 0
    }));

    return stats;
  }

  private async Task<List<AdvertNotificationStats>> GetLinkedNotificationStatsForAdverts(
      List<Guid> advertIds, DateTime fromDate, CancellationToken cancellationToken)
  {
    // Fast query using the relationship
    var stats = await _tradingContext.Notifications
        .Where(x => advertIds.Contains(x.AdvertId) && x.Added >= fromDate)
        .GroupBy(x => x.AdvertId)
        .Select(g => new AdvertNotificationStats
        {
          AdvertId = g.Key,
          ContactsNotifiedAbout = g.Select(n => n.ContactId).Distinct().Count(),
          LinkedNotifications = g.Count(n => n.SavedSearchId.HasValue),
          UnlinkedNotifications = g.Count(n => !n.SavedSearchId.HasValue)
        })
        .AsNoTracking()
        .ToListAsync(cancellationToken);

    // Add entries for adverts with no notifications
    var missingAdvertIds = advertIds.Except(stats.Select(s => s.AdvertId)).ToList();
    stats.AddRange(missingAdvertIds.Select(id => new AdvertNotificationStats
    {
      AdvertId = id,
      ContactsNotifiedAbout = 0,
      LinkedNotifications = 0,
      UnlinkedNotifications = 0
    }));

    return stats;
  }

  private async Task<List<AdvertSearchMatchStats>> GetEstimatedSearchMatchStatsFromNotifications(
      List<Guid> advertIds, DateTime fromDate, CancellationToken cancellationToken)
  {
    // Use notification data to estimate search matches - much faster than running actual searches
    var stats = await _tradingContext.Notifications
        .Where(x => advertIds.Contains(x.AdvertId) &&
                   x.Added >= fromDate &&
                   x.SavedSearchId.HasValue)
        .Include(x => x.SavedSearch)
        .GroupBy(x => x.AdvertId)
        .Select(g => new AdvertSearchMatchStats
        {
          AdvertId = g.Key,
          SavedSearchMatches = g.Select(n => n.SavedSearchId).Distinct().Count(),
          UnsavedSearchMatches = 0 // We can't estimate this from notifications
        })
        .AsNoTracking()
        .ToListAsync(cancellationToken);

    // Add entries for adverts with no notifications
    var missingAdvertIds = advertIds.Except(stats.Select(s => s.AdvertId)).ToList();
    stats.AddRange(missingAdvertIds.Select(id => new AdvertSearchMatchStats
    {
      AdvertId = id,
      SavedSearchMatches = 0,
      UnsavedSearchMatches = 0
    }));

    return stats;
  }

  private async Task<List<AdvertSearchMatchStats>> GetReverseSearchMatchStatsForAdverts(
      List<Guid> advertIds, DateTime fromDate, CancellationToken cancellationToken)
  {
    // For single advert lookups, still do the detailed search matching
    // But use reverse approach: get all searches, then check if they match these adverts
    var results = new List<AdvertSearchMatchStats>();

    // Get all active saved searches
    var savedSearches = await _tradingContext.SavedSearches
        .Include(x => x.Search)
        .Where(x => x.StatusId == (int)StatusEnum.Active && x.Search != null)
        .AsNoTracking()
        .ToListAsync(cancellationToken);

    // For each advert, check how many searches match it
    foreach (var advertId in advertIds)
    {
      var savedSearchMatches = 0;
      var unsavedSearchMatches = 0;

      try
      {
        var advertIdFilter = GuidHelper.Guid2Crc(advertId);

        // Check each saved search to see if it matches this advert
        foreach (var savedSearch in savedSearches)
        {
          var phrase = BuildSearchPhrase(savedSearch.Search.SearchPhrase, new[] { advertIdFilter });
          var matches = await _advertSearchRepository.GetSphAdvertsByPhraseAsync(phrase, cancellationToken);

          if (matches.Any(x => x.SphLink.Advert.Id == advertId))
          {
            savedSearchMatches++;
          }
        }

        // For unsaved searches, we could implement similar logic but it's expensive
        // Leave as 0 for now unless specifically needed
        unsavedSearchMatches = 0;
      }
      catch
      {
        // Continue with zeros for this advert if search fails
      }

      results.Add(new AdvertSearchMatchStats
      {
        AdvertId = advertId,
        SavedSearchMatches = savedSearchMatches,
        UnsavedSearchMatches = unsavedSearchMatches
      });
    }

    return results;
  }

  private async Task<List<SavedSearchMatchData>> GetMatchDataForSavedSearches(
      List<Guid> savedSearchIds, DateTime fromDate, CancellationToken cancellationToken)
  {
    var results = new List<SavedSearchMatchData>();

    // Get recent adverts for matching
    var recentAdverts = await _tradingContext.Adverts
        .Include(x => x.Vehicle)
        .AsNoTracking()
        .Where(x => x.StatusId == (int)StatusEnum.Active && x.Updated >= fromDate)
        .Select(x => new RecentAdvertDataDTO { Id = x.Id, Vrm = x.Vehicle.Vrm })
        .ToListAsync(cancellationToken);

    if (!recentAdverts.Any())
    {
      return savedSearchIds.Select(id => new SavedSearchMatchData
      {
        SavedSearchId = id,
        MatchCount = 0,
        SimplifiedAdverts = new List<SimplifiedAdvertReferenceDTO>()
      }).ToList();
    }

    var advertIds = recentAdverts.Select(x => x.Id).ToList();

    // Process searches in batches
    var batchSize = 10;
    for (int i = 0; i < savedSearchIds.Count; i += batchSize)
    {
      var batch = savedSearchIds.Skip(i).Take(batchSize).ToList();
      var batchResults = await ProcessSearchBatchForMatchData(batch, recentAdverts, advertIds, cancellationToken);
      results.AddRange(batchResults);
    }

    return results;
  }

  private async Task<List<SavedSearchMatchData>> ProcessSearchBatchForMatchData(
      List<Guid> savedSearchIds, List<RecentAdvertDataDTO> recentAdverts, List<Guid> advertIds, CancellationToken cancellationToken)
  {
    var results = new List<SavedSearchMatchData>();
    var recentAdvertIdSet = advertIds.ToHashSet();

    var searches = await _tradingContext.SavedSearches
        .Include(x => x.Search)
        .Where(x => savedSearchIds.Contains(x.Id))
        .AsNoTracking()
        .ToListAsync(cancellationToken);

    foreach (var search in searches)
    {
      try
      {
        if (search.Search?.SearchPhrase == null)
        {
          results.Add(new SavedSearchMatchData
          {
            SavedSearchId = search.Id,
            MatchCount = 0,
            SimplifiedAdverts = new List<SimplifiedAdvertReferenceDTO>()
          });
          continue;
        }

        // Get matching adverts from Sphinx
        var allMatchingAdverts = await _advertSearchRepository.GetSphAdvertsByPhraseAsync(
            search.Search.SearchPhrase, cancellationToken);

        // Filter to only recent adverts
        var recentMatchingAdverts = allMatchingAdverts
            .Where(x => recentAdvertIdSet.Contains(x.SphLink.Advert.Id))
            .ToList();

        var matchCount = recentMatchingAdverts.Count;

        // Get simplified advert references
        var simplifiedAdverts = recentMatchingAdverts
            .Take(10) // Limit to first 10 for performance
            .Select(sphinxResult => {
              var advertInfo = recentAdverts.First(x => x.Id == sphinxResult.SphLink.Advert.Id);
              return new SimplifiedAdvertReferenceDTO
              {
                AdvertId = advertInfo.Id,
                Vrm = advertInfo.Vrm
              };
            })
            .ToList();

        results.Add(new SavedSearchMatchData
        {
          SavedSearchId = search.Id,
          MatchCount = matchCount,
          SimplifiedAdverts = simplifiedAdverts
        });
      }
      catch
      {
        results.Add(new SavedSearchMatchData
        {
          SavedSearchId = search.Id,
          MatchCount = 0,
          SimplifiedAdverts = new List<SimplifiedAdvertReferenceDTO>()
        });
      }
    }

    return results;
  }

  private async Task<LinkedSystemNotificationMetrics> CalculateLinkedSystemNotificationMetrics(
      DateTime fromDate, CancellationToken cancellationToken)
  {
    // Get comprehensive notification statistics using the new relationship
    var notificationData = await _tradingContext.Notifications
        .Where(x => x.Added >= fromDate)
        .GroupBy(x => 1) // Group all into one result
        .Select(g => new
        {
          TotalNotifications = g.Count(),
          LinkedNotifications = g.Count(n => n.SavedSearchId.HasValue),
          UnlinkedNotifications = g.Count(n => !n.SavedSearchId.HasValue),
          TotalAdvertsNotifiedAbout = g.Select(n => n.AdvertId).Distinct().Count(),
          TotalContactsNotified = g.Select(n => n.ContactId).Distinct().Count(),
          AdvertsWithSavedSearchMatches = g.Where(n => n.SavedSearchId.HasValue).Select(n => n.AdvertId).Distinct().Count()
        })
        .AsNoTracking()
        .FirstOrDefaultAsync(cancellationToken);

    if (notificationData == null)
    {
      return new LinkedSystemNotificationMetrics
      {
        TotalAdvertsNotifiedAbout = 0,
        TotalContactsNotified = 0,
        AdvertsWithSavedSearchMatches = 0,
        AdvertsWithUnsavedSearchMatches = 0,
        LinkedNotificationPercentage = 0
      };
    }

    return new LinkedSystemNotificationMetrics
    {
      TotalAdvertsNotifiedAbout = notificationData.TotalAdvertsNotifiedAbout,
      TotalContactsNotified = notificationData.TotalContactsNotified,
      AdvertsWithSavedSearchMatches = notificationData.AdvertsWithSavedSearchMatches,
      AdvertsWithUnsavedSearchMatches = 0, // Placeholder - would need additional logic
      LinkedNotificationPercentage = notificationData.TotalNotifications > 0
            ? (decimal)notificationData.LinkedNotifications / notificationData.TotalNotifications * 100
            : 0
    };
  }

  private string BuildSearchPhrase(string originalPhrase, IEnumerable<uint> advertIds)
  {
    var filterToInsert = $"advertIdCRC,{string.Join(',', advertIds)}";

    var filterIndex = originalPhrase.IndexOf(";filter=", StringComparison.Ordinal);
    if (filterIndex >= 0)
    {
      return originalPhrase.Insert(filterIndex + 8, filterToInsert + ",");
    }

    var firstSemicolon = originalPhrase.IndexOf(';');
    if (firstSemicolon >= 0)
    {
      return originalPhrase.Insert(firstSemicolon, $";filter={filterToInsert}");
    }

    return $"{originalPhrase};filter={filterToInsert}";
  }

  private List<AdminCustomerSearchAnalyticsDTO> ApplyCustomerOrdering(
      List<AdminCustomerSearchAnalyticsDTO> customers, List<OrderByDTO> orderBy)
  {
    if (orderBy?.Any() != true)
      return customers.OrderBy(x => x.CustomerName).ToList();

    foreach (var order in orderBy)
    {
      switch (order.Column?.ToLower())
      {
        case "customername":
          customers = order.Descending == true
              ? customers.OrderByDescending(x => x.CustomerName).ToList()
              : customers.OrderBy(x => x.CustomerName).ToList();
          break;
        case "totalsavedsearches":
          customers = order.Descending == true
              ? customers.OrderByDescending(x => x.TotalSavedSearches).ToList()
              : customers.OrderBy(x => x.TotalSavedSearches).ToList();
          break;
        case "notificationeffectivenessrate":
          customers = order.Descending == true
              ? customers.OrderByDescending(x => x.NotificationEffectivenessRate).ToList()
              : customers.OrderBy(x => x.NotificationEffectivenessRate).ToList();
          break;
      }
    }

    return customers;
  }

  private IQueryable<Advert> ApplyAdvertOrdering(IQueryable<Advert> query, List<OrderByDTO> orderBy)
  {
    if (orderBy?.Any() != true)
      return query.OrderByDescending(x => x.Updated);

    foreach (var order in orderBy)
    {
      switch (order.Column?.ToLower())
      {
        case "advertstatus":
          query = order.Descending == true
            ? query.OrderByDescending(x => x.AdvertStatus)
            : query.OrderBy(x => x.AdvertStatus);
          break;
        case "soldstatus":
          query = order.Descending == true
            ? query.OrderByDescending(x => x.SoldStatus)
            : query.OrderBy(x => x.SoldStatus);
          break;
        case "customername":
          query = order.Descending == true
            ? query.OrderByDescending(x => x.Customer.CustomerName)
            : query.OrderBy(x => x.Customer.CustomerName);
          break;
        case "vrm":
          query = order.Descending == true
              ? query.OrderByDescending(x => x.Vehicle.Vrm)
              : query.OrderBy(x => x.Vehicle.Vrm);
          break;
        case "updated":
          query = order.Descending == true
              ? query.OrderByDescending(x => x.Updated)
              : query.OrderBy(x => x.Updated);
          break;
        case "added":
          query = order.Descending == true
              ? query.OrderByDescending(x => x.Added)
              : query.OrderBy(x => x.Added);
          break;
      }
    }

    return query;
  }

  public async Task<List<AdminStatVendorDTO>> GetAdminStatVendors(CancellationToken cancellationToken)
  {
    var vendors = await _tradingContext.Customers
      .AsNoTracking()
      .Where(x => x.StatusId == (uint)StatusEnum.Active && x.TotalAdverts > 0)
      .Select(x => new AdminStatVendorDTO
      {
        Id = x.Id,
        VendorName = x.CustomerName,
        //TotalAdverts = x.TotalAdverts,
        //AssignedTo = x.CustomerInternalInfo.AssignedTo
      }).ToListAsync(cancellationToken);

    return vendors.OrderBy(x => x.VendorName).ToList();
  }

  public Task<List<AdminStatBasicCustomerDTO>> GetCustomersWithoutAlerts(CancellationToken cancellationToken)
  {
    var customers = _tradingContext.Customers
      .AsNoTracking()
      .Where(x => x.StatusId == (uint)StatusEnum.Active && !x.Contacts.Any(c => c.SavedSearches.Any()))
      .Select(x => new AdminStatBasicCustomerDTO
      {
        Id = x.Id,
        CustomerName = x.CustomerName,
        IsBuyer = x.IsBuyer,
        IsSeller = x.IsSeller,
        LastAdvert = x.LastAdvert,
        TotalAdverts = x.TotalAdverts,
        LastLogin = x.LastLogin,
        LiveAdverts = x.LiveAdverts,  
      });

    return customers
        .OrderBy(x => x.CustomerName)
        .ToListAsync(cancellationToken);
  }
}

// Updated helper classes to take advantage of the new relationship
internal class SavedSearchNotificationStats
{
  public Guid SavedSearchId { get; set; }
  public int DistinctAdvertsNotifiedAbout { get; set; }
  public int TotalNotifications { get; set; }
}

internal class AdvertNotificationStats
{
  public Guid AdvertId { get; set; }
  public int ContactsNotifiedAbout { get; set; }
  public int LinkedNotifications { get; set; }
  public int UnlinkedNotifications { get; set; }
}

internal class SavedSearchMatchData
{
  public Guid SavedSearchId { get; set; }
  public int MatchCount { get; set; }
  public List<SimplifiedAdvertReferenceDTO> SimplifiedAdverts { get; set; }
}

internal class AdvertSearchMatchStats
{
  public Guid AdvertId { get; set; }
  public int SavedSearchMatches { get; set; }
  public int UnsavedSearchMatches { get; set; }
}

internal class LinkedSystemNotificationMetrics
{
  public int TotalAdvertsNotifiedAbout { get; set; }
  public int TotalContactsNotified { get; set; }
  public int AdvertsWithSavedSearchMatches { get; set; }
  public int AdvertsWithUnsavedSearchMatches { get; set; }
  public decimal LinkedNotificationPercentage { get; set; }
}