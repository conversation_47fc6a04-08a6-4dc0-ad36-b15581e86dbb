﻿using Microsoft.Extensions.Logging;
using Quartz;
using System;
using System.Threading.Tasks;
using Trading.Services.Interfaces;

namespace Trading.API.Remarq.QuartzJobs
{
  public class EmailAnalyticsJob : IJob
  {
    private readonly ILogger<EmailAnalyticsJob> _logger;
    private readonly IEmailAnalyticsService _emailAnalyticsService;

    public EmailAnalyticsJob(ILogger<EmailAnalyticsJob> logger, IEmailAnalyticsService emailAnalyticsService)
    {
      _logger = logger;
      _emailAnalyticsService = emailAnalyticsService;
    }

    public async Task Execute(IJobExecutionContext context)
    {
      _logger.LogInformation("EmailAnalyticsJob started at {time}", DateTimeOffset.Now);

      try
      {
        // Store recent email events from the past 48 hours
        var newEventsCount = await _emailAnalyticsService.StoreRecentEmailEvents();

        _logger.LogInformation("EmailAnalyticsJob completed successfully at {time}. Stored {count} new email events.",
            DateTimeOffset.Now, newEventsCount);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error occurred executing EmailAnalyticsJob");
        throw;
      }
    }
  }
}
