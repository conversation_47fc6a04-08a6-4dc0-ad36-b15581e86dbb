using brevo_csharp.Model;
using System;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.Services.Interfaces;

namespace Trading.Services.Tests.FakeServices
{
  public class FakeEmailAnalyticsService : IEmailAnalyticsService
  {
    public Task<GetEmailEventReport> GetTransactionalEmailEvents(
        DateTime? startDate = null,
        DateTime? endDate = null,
        long? limit = 2500,
        long? offset = 0,
        string email = null,
        long? templateId = null,
        string messageId = null,
        string eventType = null)
    {
      // Return a fake email event report for testing
      var report = new GetEmailEventReport
      {
        Events = new System.Collections.Generic.List<GetEmailEventReportEvents>
        {
          new GetEmailEventReportEvents
          {
            Email = "<EMAIL>",
            Event = GetEmailEventReportEvents.EventEnum.Delivered,
            Date = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
            MessageId = "fake-message-id-123",
            Subject = "Test Email Subject",
            Tag = "alert-advert-12345678-1234-1234-1234-123456789012"
          }
        }
      };
      return Task.FromResult(report);
    }

    public Task<GetAggregatedReport> GetTransactionalEmailStats(
        DateTime? startDate = null,
        DateTime? endDate = null,
        long? days = null,
        string tag = null)
    {
      // Return fake aggregated stats for testing
      var stats = new GetAggregatedReport
      {
        Delivered = 100,
        Opens = 50,
        UniqueOpens = 45,
        Clicks = 20,
        UniqueClicks = 18,
        HardBounces = 2,
        SoftBounces = 3,
        Blocked = 1,
        Invalid = 0,
        Unsubscribed = 1
      };
      return Task.FromResult(stats);
    }

    public Task<GetReports> GetDailyEmailStats(
        DateTime? startDate = null,
        DateTime? endDate = null,
        long? days = null,
        string tag = null)
    {
      // Return fake daily stats for testing
      var reports = new GetReports
      {
        Reports = new System.Collections.Generic.List<GetReportsReports>
        {
          new GetReportsReports
          {
            Date = DateTime.UtcNow.ToString("yyyy-MM-dd"),
            Delivered = 50,
            Opens = 25,
            UniqueOpens = 22,
            Clicks = 10,
            UniqueClicks = 9
          }
        }
      };
      return Task.FromResult(reports);
    }

    public Task<GetEmailEventReport> GetEmailEventsForAddress(
        string emailAddress,
        DateTime? startDate = null,
        DateTime? endDate = null,
        long? limit = 100)
    {
      // Return fake events for specific email address
      var report = new GetEmailEventReport
      {
        Events = new System.Collections.Generic.List<GetEmailEventReportEvents>
        {
          new GetEmailEventReportEvents
          {
            Email = emailAddress,
            Event = GetEmailEventReportEvents.EventEnum.Opened,
            Date = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
            MessageId = "fake-message-id-456",
            Subject = "Test Email for Specific Address"
          }
        }
      };
      return Task.FromResult(report);
    }

    public Task<decimal?> GetTransactionalClickThroughRate(
        DateTime? startDate = null,
        DateTime? endDate = null,
        string tag = null)
    {
      // Return fake CTR for testing (18%)
      return Task.FromResult<decimal?>(0.18m);
    }

    public Task<TransactionalEmailMetricsDTO> GetTransactionalEmailMetrics(
        DateTime? startDate = null,
        DateTime? endDate = null,
        string tag = null)
    {
      // Return fake comprehensive metrics for testing
      var metrics = new TransactionalEmailMetricsDTO
      {
        StartDate = startDate,
        EndDate = endDate,
        Tag = tag,
        Sent = 105,
        Delivered = 100,
        Opens = 50,
        UniqueOpens = 45,
        Clicks = 20,
        UniqueClicks = 18,
        Bounces = 5,
        HardBounces = 2,
        SoftBounces = 3,
        Blocked = 1,
        Invalid = 0,
        Unsubscribed = 1,
        DeliveryRate = 0.952m, // 100/105
        OpenRate = 0.45m,      // 45/100
        ClickThroughRate = 0.18m, // 18/100
        BounceRate = 0.048m    // 5/105
      };
      return Task.FromResult(metrics);
    }

    public Task<GetEmailEventReport> GetEmailsForAdvert(
        string advertTag,
        DateTime? startDate = null,
        DateTime? endDate = null)
    {
      // Return fake events for specific advert
      var report = new GetEmailEventReport
      {
        Events = new System.Collections.Generic.List<GetEmailEventReportEvents>
        {
          new GetEmailEventReportEvents
          {
            Email = "<EMAIL>",
            Event = GetEmailEventReportEvents.EventEnum.Click,
            Date = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
            MessageId = "fake-message-id-789",
            Subject = "Vehicle Alert",
            Tag = advertTag
          }
        }
      };
      return Task.FromResult(report);
    }

    public Task<int> StoreRecentEmailEvents()
    {
      // Return fake count of stored events for testing
      return Task.FromResult(5);
    }
  }
}
