﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Trading.API.Data.Migrations
{
    /// <inheritdoc />
    public partial class AdvertEventStorage_Index : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "idx_AdvertEvent_Duplicate_Check",
                table: "AdvertEvent");

            migrationBuilder.CreateIndex(
                name: "idx_AdvertEvent_Duplicate_Check",
                table: "AdvertEvent",
                columns: new[] { "Tag", "Event", "Email" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "idx_AdvertEvent_Duplicate_Check",
                table: "AdvertEvent");

            migrationBuilder.CreateIndex(
                name: "idx_AdvertEvent_Duplicate_Check",
                table: "AdvertEvent",
                columns: new[] { "MessageId", "Event", "Email" });
        }
    }
}
