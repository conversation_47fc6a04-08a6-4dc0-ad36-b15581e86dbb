using AutoMapper;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.AutoTrader;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.Services.ExternalDTO.Configs;
using Trading.Services.Interfaces;
using Trading.Services.Interfaces.AutoTrader;

namespace Trading.Services.Classes.AutoTrader;
public class AutoTraderVRMLookupService : IVRMLookupService
{
  private readonly TradingContext _tradingContext;
  private readonly IMapper _mapper;
  private readonly ILookupService _lookupService;
  private readonly IAutoTraderClient _autoTraderClient;
  private readonly AutoTraderDTO _config;

  public AutoTraderVRMLookupService(TradingContext tradingContext, IMapper mapper,
      ILookupService lookupService, IAutoTraderClient autoTraderClient, IOptionsSnapshot<AutoTraderDTO> config)
  {
    _tradingContext = tradingContext;
    _mapper = mapper;
    _lookupService = lookupService;
    _autoTraderClient = autoTraderClient;
    _config = config.Value;
  }

  public Task<VehicleCheckDTO> GetVehicleCheckData(VRMLookupDataDTO lookupDTO, VehicleCheckTypeEnum checkType)
  {
    throw new NotImplementedException();
  }

  public async Task<VehicleLookupInfoDTO> GetVehicleData(VRMLookupDataDTO lookupDTO, CancellationToken cancellationToken, bool? useCache = true)
  {
    // Build the endpoint with all data types
    var endpoint = $"/vehicles?registration={Uri.EscapeDataString(lookupDTO.vrm)}&odometerReadingMiles={lookupDTO.odometer}&features=true&history=true&valuations=true&vehicleMetrics=true";
    endpoint += $"&advertiserId={Uri.EscapeDataString(_config.DealerId)}";
    
    var response = await _autoTraderClient.GetAsync<ATCombinedVehicleResponse>(endpoint, cancellationToken);

    if (response == null || response.Vehicle == null || string.IsNullOrEmpty(response.Vehicle.Make))
    {
      return null; // No data found for the given VRM
    }

    // Map the response to VehicleLookupInfoDTO
    var vehicleInfo = _mapper.Map<VehicleLookupInfoDTO>(response.Vehicle);
  }

  public async Task<VehicleLookupInfo> ParseResponse(string vrm, string content, CancellationToken ct)
  {
    var info = new VehicleLookupInfo();



    // Always set these properties
    info.VRM = vrm?.ToUpperInvariant() ?? "UNKNOWN";
    info.StatusId = (uint)StatusEnum.Active;
    info.Added = DateTime.Now;
    info.JsonBlob = content ?? "";
    info.ServiceProvider = "AutoTrader";
    info.CacheExpiryDate = DateTime.Now.AddDays(20);

    return info;
  }
}
