using AutoMapper;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.AutoTrader;
using Trading.API.Data.Enums;
using Trading.API.Data.Models;
using Trading.Services.ExternalDTO.Configs;
using Trading.Services.Interfaces;
using Trading.Services.Interfaces.AutoTrader;

namespace Trading.Services.Classes.AutoTrader;
public class AutoTraderVRMLookupService : IVRMLookupService
{
  private readonly TradingContext _tradingContext;
  private readonly IMapper _mapper;
  private readonly ILookupService _lookupService;
  private readonly IAutoTraderClient _autoTraderClient;
  private readonly AutoTraderDTO _config;

  public AutoTraderVRMLookupService(TradingContext tradingContext, IMapper mapper,
      ILookupService lookupService, IAutoTraderClient autoTraderClient, IOptionsSnapshot<AutoTraderDTO> config)
  {
    _tradingContext = tradingContext;
    _mapper = mapper;
    _lookupService = lookupService;
    _autoTraderClient = autoTraderClient;
    _config = config.Value;
  }

  public Task<VehicleCheckDTO> GetVehicleCheckData(VRMLookupDataDTO lookupDTO, VehicleCheckTypeEnum checkType)
  {
    throw new NotImplementedException();
  }

  public async Task<VehicleLookupInfoDTO> GetVehicleData(VRMLookupDataDTO lookupDTO, CancellationToken cancellationToken, bool? useCache = true)
  {
    VehicleLookupInfo info = null;
    string dataBlob = null;

    if (useCache == true)
    {
      var existingVehicleInfo = await _lookupService.GetLookupCache(lookupDTO, cancellationToken);

      if (existingVehicleInfo != null)
      {
        info = existingVehicleInfo;
        dataBlob = existingVehicleInfo.JsonBlob;
      }
    }

    // Make Request if not cached
    if (info == null)
    {
      // Build the endpoint with all data types
      var endpoint = $"/vehicles?registration={Uri.EscapeDataString(lookupDTO.vrm)}&odometerReadingMiles={lookupDTO.odometer}&features=true&history=true&valuations=true&vehicleMetrics=true";
      endpoint += $"&advertiserId={Uri.EscapeDataString(_config.DealerId)}";

      var response = await _autoTraderClient.GetAsync<ATCombinedVehicleResponse>(endpoint, cancellationToken);

      if (response == null || response.Vehicle == null || string.IsNullOrEmpty(response.Vehicle.Make))
      {
        return null; // No data found for the given VRM
      }

      // Serialize the response to JSON for storage
      dataBlob = JsonConvert.SerializeObject(response);
    }

    // Parse Response
    var parsedResponse = await ParseResponse(lookupDTO.vrm, dataBlob, cancellationToken);
    var lookup = info ?? parsedResponse;
    lookup.Odometer = (lookupDTO.odometer ?? 0).ToString();

    if (useCache == true && info == null)
    {
      await _lookupService.SaveLookupCache(lookup, cancellationToken);
    }

    return _mapper.Map<VehicleLookupInfoDTO>(lookup);
  }

  public async Task<VehicleLookupInfo> ParseResponse(string vrm, string content, CancellationToken ct)
  {
    var info = new VehicleLookupInfo();

    try
    {
      if (!string.IsNullOrEmpty(content))
      {
        var response = JsonConvert.DeserializeObject<ATCombinedVehicleResponse>(content);

        if (response?.Vehicle != null)
        {
          var vehicle = response.Vehicle;

          // Basic vehicle information
          info.VRM = vehicle.Registration?.ToUpperInvariant() ?? vrm?.ToUpperInvariant() ?? "UNKNOWN";
          info.VIN = vehicle.Vin?.ToUpperInvariant();
          info.MakeName = vehicle.Make;
          info.ModelName = vehicle.Model;
          info.DerivName = vehicle.Derivative;
          info.BodyTypeName = vehicle.BodyType;
          info.FuelTypeName = vehicle.FuelType;
          info.TransmissionTypeName = vehicle.TransmissionType;
          info.VehicleTypeName = vehicle.VehicleType;

          // Engine and performance data
          info.EngineCC = vehicle.EngineCapacityCC?.ToString();
          info.BHP = vehicle.EnginePowerBHP?.ToString(); // Fixed property name
          info.CO2 = vehicle.Co2EmissionGPKM?.ToString(); // Fixed property name

          // Physical characteristics
          info.Doors = vehicle.Doors?.ToString();
          info.Colour = vehicle.Colour;
          info.Weight = vehicle.MinimumKerbWeightKG?.ToString(); // AutoTrader provides minimum kerb weight

          // Registration and history data - AutoTrader provides actual registration date
          if (vehicle.FirstRegistrationDate.HasValue)
          {
            info.DateRegistered = vehicle.FirstRegistrationDate.Value.ToString("dd/MM/yyyy");
          }

          // Transmission - use correct property name
          info.TransmissionTypeName = vehicle.TransmissionType;

          // Year of manufacture from History data (not available in main vehicle object)
          if (response.History?.YearOfManufacture.HasValue == true)
          {
            info.YearOfManufacture = response.History.YearOfManufacture;
          }

          // Previous keepers from history data
          if (response.History?.PreviousOwners.HasValue == true)
          {
            info.PreviousKeepers = response.History.PreviousOwners.ToString();
          }

          // Import status from history data
          if (response.History?.Imported == true)
          {
            info.Imported = "Yes";
          }
          else if (response.History?.Imported == false)
          {
            info.Imported = "No";
          }

          // Color change detection - this would be in a full vehicle check response, not basic lookup
          // For now, we'll set this to false as it's not available in the basic vehicle lookup
          info.ColourChanged = false;

          // Euro status - AutoTrader provides emission class data
          if (!string.IsNullOrEmpty(vehicle.EmissionClass))
          {
            info.EuroStatus = vehicle.EmissionClass;
          }

          // Additional AutoTrader-specific data that CAP doesn't provide
          // Store additional valuable information in a structured way
          var additionalData = new
          {
            Seats = vehicle.Seats,
            Cylinders = vehicle.Cylinders,
            Valves = vehicle.Valves,
            EngineTorqueNM = vehicle.EngineTorqueNM,
            TopSpeedMPH = vehicle.TopSpeedMPH,
            ZeroToSixtyMPHSeconds = vehicle.ZeroToSixtyMPHSeconds,
            BadgeEngineSizeLitres = vehicle.BadgeEngineSizeLitres,
            FuelCapacityLitres = vehicle.FuelCapacityLitres,
            InsuranceGroup = vehicle.InsuranceGroup,
            InsuranceSecurityCode = vehicle.InsuranceSecurityCode,
            FuelEconomyCombinedMPG = vehicle.FuelEconomyWLTPCombinedMPG ?? vehicle.FuelEconomyNEDCCombinedMPG,
            BootSpaceSeatsUpLitres = vehicle.BootSpaceSeatsUpLitres,
            LengthMM = vehicle.LengthMM,
            HeightMM = vehicle.HeightMM,
            WidthMM = vehicle.WidthMM,
            GrossVehicleWeightKG = vehicle.GrossVehicleWeightKG,
            Drivetrain = vehicle.Drivetrain,
            Generation = vehicle.Generation,
            Trim = vehicle.Trim,
            CountryOfOrigin = vehicle.CountryOfOrigin,
            BatteryRangeMiles = vehicle.BatteryRangeMiles, // For electric vehicles
            BatteryCapacityKWH = vehicle.BatteryCapacityKWH,
            StartStop = vehicle.StartStop,
            Gears = vehicle.Gears
          };

          // Store additional data in JsonBlob for future use
          var extendedData = new
          {
            AutoTraderData = additionalData,
            Features = response.Features,
            Valuations = response.Valuations,
            VehicleMetrics = response.VehicleMetrics
          };

          // Update JsonBlob with enriched data
          info.JsonBlob = JsonConvert.SerializeObject(extendedData);

          // Try to get plate information from registration date
          if (!string.IsNullOrEmpty(info.DateRegistered) && DateTime.TryParse(info.DateRegistered, out DateTime parsedDate))
          {
            try
            {
              var plate = await _lookupService.GetPlateFromDate(parsedDate, ct);
              info.PlateName = plate?.PlateName ?? "No Plate Found";
            }
            catch (Exception ex)
            {
              info.PlateName = "No Plate Found";
            }
          }
          else
          {
            info.PlateName = "No Plate Found";
          }
        }
        else
        {
          info.InvalidVRM = true;
        }
      }
      else
      {
        info.InvalidVRM = true;
      }
    }
    catch (Exception ex)
    {
      info.InvalidVRM = true;
    }

    // Always set these properties
    info.VRM = info.VRM ?? vrm?.ToUpperInvariant() ?? "UNKNOWN";
    info.StatusId = (uint)StatusEnum.Active;
    info.Added = DateTime.Now;

    // Only set JsonBlob to original content if it wasn't already enriched during parsing
    if (string.IsNullOrEmpty(info.JsonBlob))
    {
      info.JsonBlob = content ?? "";
    }

    info.ServiceProvider = "AutoTrader";
    info.CacheExpiryDate = DateTime.Now.AddDays(20);

    return info;
  }
}
