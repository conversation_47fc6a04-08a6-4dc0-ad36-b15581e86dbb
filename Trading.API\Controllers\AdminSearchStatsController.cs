﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO.Admin;
using Trading.Services.Interfaces;

namespace Trading.API.Remarq.Controllers;

[ApiController]
[Route("api/search-stats")]
//[Authorize] // Assuming you want admin-only access
public class AdminSearchStatsController : ControllerBase
{
  private readonly IAdminSearchStatsService _adminSearchStatsService;

  public AdminSearchStatsController(IAdminSearchStatsService adminSearchStatsService)
  {
    _adminSearchStatsService = adminSearchStatsService;
  }

  [HttpGet("vendors")]
  [ResponseCache(Duration = 60, Location = ResponseCacheLocation.Any, NoStore = false)]
  public async Task<IActionResult> GetVendors(CancellationToken cancellationToken = default)
  {
    try
    {
      var result = await _adminSearchStatsService.GetAdminStatVendors(cancellationToken);
      return Ok(result);
    }
    catch (Exception ex)
    {
      return StatusCode(500, new { message = "An error occurred while retrieving vendors", error = ex.Message });
    }
  }

  [HttpGet("customers-without-alerts")]
  [ResponseCache(Duration = 60, Location = ResponseCacheLocation.Any, NoStore = false)]
  public async Task<IActionResult> GetCustomersWithNoAlerts(CancellationToken cancellationToken = default)
  {
    try
    {
      var result = await _adminSearchStatsService.GetCustomersWithoutAlerts(cancellationToken);
      return Ok(result);
    }
    catch (Exception ex)
    {
      return StatusCode(500, new { message = "An error occurred while retrieving vendors", error = ex.Message });
    }
  }

  /// <summary>
  /// Get customers grouped with their saved searches and comprehensive analytics
  /// Main admin screen endpoint for customer-centric search analysis
  /// </summary>
  /// <param name="request">Filter and pagination parameters</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>Paginated list of customers with their search analytics</returns>
  [HttpPost("customer-analytics")]
  public async Task<IActionResult> GetCustomerSearchAnalytics(
      [FromBody] AdminCustomerSearchAnalyticsRequestDTO request,
      CancellationToken cancellationToken = default)
  {
    try
    {
      var result = await _adminSearchStatsService.GetCustomerSearchAnalytics(request, cancellationToken);
      return Ok(result);
    }
    catch (Exception ex)
    {
      return StatusCode(500, new { message = "An error occurred while retrieving customer search analytics", error = ex.Message });
    }
  }

  /// <summary>
  /// Get advert-centric notification analytics
  /// Provides detailed metrics for how adverts perform across different search types
  /// </summary>
  /// <param name="request">Filter and pagination parameters</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>Paginated list of advert notification analytics</returns>
  [HttpPost("advert-analytics")]
  public async Task<IActionResult> GetAdvertNotificationAnalytics(
      [FromBody] AdminAdvertAnalyticsRequestDTO request,
      CancellationToken cancellationToken = default)
  {
    try
    {
      var result = await _adminSearchStatsService.GetAdvertNotificationAnalytics(request, cancellationToken);
      return Ok(result);
    }
    catch (Exception ex)
    {
      return StatusCode(500, new { message = "An error occurred while retrieving advert analytics", error = ex.Message });
    }
  }

  /// <summary>
  /// Get comprehensive system overview with health indicators
  /// Dashboard summary data with key performance metrics
  /// </summary>
  /// <param name="fromDate">Optional start date for metrics (defaults to 30 days ago)</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>System overview with comprehensive metrics</returns>
  [HttpGet("system-overview")]
  public async Task<IActionResult> GetSearchSystemOverview(
      [FromQuery] DateTime? fromDate = null,
      CancellationToken cancellationToken = default)
  {
    try
    {
      var result = await _adminSearchStatsService.GetSearchSystemOverview(fromDate, cancellationToken);
      return Ok(result);
    }
    catch (Exception ex)
    {
      return StatusCode(500, new { message = "An error occurred while retrieving system overview", error = ex.Message });
    }
  }

  /// <summary>
  /// Get analytics for a specific customer
  /// Useful for customer detail popups or drill-down views
  /// </summary>
  /// <param name="customerId">Customer ID</param>
  /// <param name="fromDate">Optional start date (defaults to 30 days ago)</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>Customer-specific search analytics</returns>
  [HttpGet("customers/{customerId:guid}/analytics")]
  public async Task<IActionResult> GetCustomerAnalytics(
      [FromRoute] Guid customerId,
      [FromQuery] DateTime? fromDate = null,
      CancellationToken cancellationToken = default)
  {
    try
    {
      var request = new AdminCustomerSearchAnalyticsRequestDTO
      {
        Filters = new AdminCustomerSearchFiltersDTO { Id = customerId, FromDate = fromDate },
        Limit = 1
      };

      var result = await _adminSearchStatsService.GetCustomerSearchAnalytics(request, cancellationToken);
      var customer = result.Results?.FirstOrDefault();

      if (customer == null)
      {
        return NotFound(new { message = "Customer not found or has no saved searches" });
      }

      return Ok(customer);
    }
    catch (Exception ex)
    {
      return StatusCode(500, new { message = "An error occurred while retrieving customer analytics", error = ex.Message });
    }
  }

  /// <summary>
  /// Get analytics for a specific advert
  /// Detailed drill-down for individual advert performance
  /// </summary>
  /// <param name="advertId">Advert ID</param>
  /// <param name="fromDate">Optional start date (defaults to 30 days ago)</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>Detailed analytics for the advert</returns>
  [HttpGet("adverts/{advertId:guid}/analytics")]
  public async Task<IActionResult> GetAdvertAnalytics(
      [FromRoute] Guid advertId,
      [FromQuery] DateTime? fromDate = null,
      CancellationToken cancellationToken = default)
  {
    try
    {
      var request = new AdminAdvertAnalyticsRequestDTO
      {
        Filters = new AdminAdvertNotificationFiltersDTO { AdvertId = advertId, FromDate = fromDate },
        Limit = 1
      };

      var result = await _adminSearchStatsService.GetAdvertNotificationAnalytics(request, cancellationToken);
      var advert = result.Results?.FirstOrDefault();

      if (advert == null)
      {
        return NotFound(new { message = "Advert not found or no analytics data available" });
      }

      return Ok(advert);
    }
    catch (Exception ex)
    {
      return StatusCode(500, new { message = "An error occurred while retrieving advert analytics", error = ex.Message });
    }
  }

  /// <summary>
  /// Search adverts by VRM
  /// Quick lookup for advert analytics by vehicle registration
  /// </summary>
  /// <param name="vrm">Vehicle registration mark</param>
  /// <param name="fromDate">Optional start date (defaults to 30 days ago)</param>
  /// <param name="limit">Optional limit (defaults to 10)</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>List of adverts matching the VRM search</returns>
  [HttpGet("adverts/search")]
  public async Task<IActionResult> SearchAdvertsByVrm(
      [FromQuery] string vrm,
      [FromQuery] DateTime? fromDate = null,
      [FromQuery] int? limit = 10,
      CancellationToken cancellationToken = default)
  {
    try
    {
      if (string.IsNullOrWhiteSpace(vrm))
      {
        return BadRequest(new { message = "VRM parameter is required" });
      }

      var request = new AdminAdvertAnalyticsRequestDTO
      {
        Filters = new AdminAdvertNotificationFiltersDTO { Vrm = vrm, FromDate = fromDate },
        Limit = limit
      };

      var result = await _adminSearchStatsService.GetAdvertNotificationAnalytics(request, cancellationToken);
      return Ok(result);
    }
    catch (Exception ex)
    {
      return StatusCode(500, new { message = "An error occurred while searching adverts", error = ex.Message });
    }
  }

  /// <summary>
  /// Export customer analytics data to CSV
  /// Useful for reporting and analysis in external tools
  /// </summary>
  /// <param name="request">Filter parameters</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>CSV file download</returns>
  [HttpPost("customer-analytics/export")]
  public async Task<IActionResult> ExportCustomerAnalytics(
      [FromBody] AdminCustomerSearchAnalyticsRequestDTO request,
      CancellationToken cancellationToken = default)
  {
    try
    {
      // Remove pagination for export
      request.Limit = null;
      request.Offset = null;

      var result = await _adminSearchStatsService.GetCustomerSearchAnalytics(request, cancellationToken);

      var csv = GenerateCustomerAnalyticsCsv(result.Results);
      var bytes = System.Text.Encoding.UTF8.GetBytes(csv);

      return File(bytes, "text/csv", $"customer-search-analytics-{DateTime.Now:yyyyMMdd}.csv");
    }
    catch (Exception ex)
    {
      return StatusCode(500, new { message = "An error occurred while exporting customer analytics", error = ex.Message });
    }
  }

  /// <summary>
  /// Export advert analytics data to CSV
  /// </summary>
  /// <param name="request">Filter parameters</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>CSV file download</returns>
  [HttpPost("advert-analytics/export")]
  public async Task<IActionResult> ExportAdvertAnalytics(
      [FromBody] AdminAdvertAnalyticsRequestDTO request,
      CancellationToken cancellationToken = default)
  {
    try
    {
      // Remove pagination for export
      request.Limit = null;
      request.Offset = null;

      var result = await _adminSearchStatsService.GetAdvertNotificationAnalytics(request, cancellationToken);

      var csv = GenerateAdvertAnalyticsCsv(result.Results);
      var bytes = System.Text.Encoding.UTF8.GetBytes(csv);

      return File(bytes, "text/csv", $"advert-analytics-{DateTime.Now:yyyyMMdd}.csv");
    }
    catch (Exception ex)
    {
      return StatusCode(500, new { message = "An error occurred while exporting advert analytics", error = ex.Message });
    }
  }

  /// <summary>
  /// Get system health check
  /// Quick endpoint to verify system operational status
  /// </summary>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>System health status</returns>
  [HttpGet("health")]
  public async Task<IActionResult> GetSystemHealth(CancellationToken cancellationToken = default)
  {
    try
    {
      var overview = await _adminSearchStatsService.GetSearchSystemOverview(DateTime.Now.AddDays(-7), cancellationToken);

      return Ok(new
      {
        Status = overview.SystemHealthStatus,
        ActiveSearches = overview.ActiveSavedSearches,
        TotalNotifications = overview.TotalAdvertsNotifiedAbout,
        CustomerEngagement = $"{overview.CustomerEngagementRate:F1}%",
        LastChecked = DateTime.Now
      });
    }
    catch (Exception ex)
    {
      return StatusCode(500, new { message = "System health check failed", error = ex.Message });
    }
  }

  /// <summary>
  /// Get top performing searches
  /// Identifies most effective searches for highlighting best practices
  /// </summary>
  /// <param name="limit">Number of top searches to return (defaults to 10)</param>
  /// <param name="fromDate">Optional start date (defaults to 30 days ago)</param>
  /// <param name="cancellationToken">Cancellation token</param>
  /// <returns>Top performing searches by notification effectiveness</returns>
  [HttpGet("top-searches")]
  public async Task<IActionResult> GetTopPerformingSearches(
      [FromQuery] int limit = 10,
      [FromQuery] DateTime? fromDate = null,
      CancellationToken cancellationToken = default)
  {
    try
    {
      var request = new AdminCustomerSearchAnalyticsRequestDTO
      {
        Filters = new AdminCustomerSearchFiltersDTO { FromDate = fromDate },
        Limit = 1000, // Get all customers first
        Order = new List<Trading.API.Data.DTO.OrderByDTO>
                {
                    new Trading.API.Data.DTO.OrderByDTO { Column = "uniquenotificationrate", Descending = true }
                }
      };

      var result = await _adminSearchStatsService.GetCustomerSearchAnalytics(request, cancellationToken);

      // Extract and rank all individual searches
      var topSearches = result.Results
          .SelectMany(customer => customer.SavedSearches.Select(search => new
          {
            CustomerName = customer.CustomerName,
            SearchName = search.SearchName,
            SearchPhrase = search.SearchPhrase,
            ContactEmail = search.ContactEmail,
            MatchingAdvertCount = search.MatchingAdvertCount,
            UniqueNotificationRate = search.NotificationEffectivenessRate,
            SearchAdvertPairRate = search.NotificationEffectivenessRate, // Same value now
            PerContactNotificationRate = search.NotificationEffectivenessRate, // Same value now,
            IsActive = search.SendUpdates == true,
            Added = search.Added
          }))
          .Where(x => x.MatchingAdvertCount > 0) // Only include searches with matches
          .OrderByDescending(x => x.UniqueNotificationRate)
          .ThenByDescending(x => x.MatchingAdvertCount)
          .Take(limit)
          .ToList();

      return Ok(topSearches);
    }
    catch (Exception ex)
    {
      return StatusCode(500, new { message = "An error occurred while retrieving top searches", error = ex.Message });
    }
  }

  #region Private Helper Methods

  private string GenerateCustomerAnalyticsCsv(IEnumerable<AdminCustomerSearchAnalyticsDTO> customers)
  {
    var csv = new System.Text.StringBuilder();
    csv.AppendLine("Customer Name,Customer Email,Assigned To,Total Searches,Active Notifications,Total Matching Adverts,Total Adverts Notified About,Notification Effectiveness Rate %");

    foreach (var customer in customers)
    {
      csv.AppendLine($"\"{customer.CustomerName}\",\"{customer.CustomerEmail}\",\"{customer.AssignedTo}\",{customer.TotalSavedSearches},{customer.ActiveNotificationSearches},{customer.TotalMatchingAdverts},{customer.TotalAdvertsNotifiedAbout},{customer.NotificationEffectivenessRate}");
    }

    return csv.ToString();
  }

  private string GenerateAdvertAnalyticsCsv(IEnumerable<AdvertNotificationStatsDTO> adverts)
  {
    var csv = new System.Text.StringBuilder();
    csv.AppendLine("Advert ID,VRM,Added,Updated,Advert Status,Sold Status,Status ID,Saved Search Matches,Unsaved Search Matches,Total Matches,Contacts Notified About,Notification Effectiveness Rate %,Notification Effectiveness");

    foreach (var advert in adverts)
    {
      csv.AppendLine($"{advert.AdvertId},\"{advert.Vrm}\",\"{advert.Added:yyyy-MM-dd}\",\"{advert.Updated:yyyy-MM-dd}\",\"{advert.AdvertStatus}\",\"{advert.SoldStatus}\",{advert.StatusId},{advert.MatchedInSavedSearchCount},{advert.MatchedInUnsavedSearchCount},{advert.TotalSearchMatches},{advert.ContactsNotifiedAbout},{advert.NotificationEffectivenessRate},\"{advert.NotificationEffectiveness}\"");
    }

    return csv.ToString();
  }

  #endregion
}