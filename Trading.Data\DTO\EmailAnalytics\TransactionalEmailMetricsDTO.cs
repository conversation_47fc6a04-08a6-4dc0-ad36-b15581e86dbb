﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.EmailAnalytics;

// DTO for comprehensive transactional email metrics
public class TransactionalEmailMetricsDTO
{
  public DateTime? StartDate { get; set; }
  public DateTime? EndDate { get; set; }
  public string Tag { get; set; }

  // Counts (using long to match Brevo API)
  public long Sent { get; set; }
  public long Delivered { get; set; }
  public long Opens { get; set; }
  public long UniqueOpens { get; set; }
  public long Clicks { get; set; }
  public long UniqueClicks { get; set; }
  public long Bounces { get; set; }
  public long HardBounces { get; set; }
  public long SoftBounces { get; set; }
  public long Blocked { get; set; }
  public long Invalid { get; set; }
  public long Unsubscribed { get; set; }

  // Rates
  public decimal DeliveryRate { get; set; }
  public decimal OpenRate { get; set; }
  public decimal ClickThroughRate { get; set; }
  public decimal BounceRate { get; set; }
}