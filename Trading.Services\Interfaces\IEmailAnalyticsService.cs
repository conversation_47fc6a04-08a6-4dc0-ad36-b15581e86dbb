using brevo_csharp.Model;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Trading.API.Data.DTO.EmailAnalytics;

namespace Trading.Services.Interfaces
{
  public interface IEmailAnalyticsService
  {
    /// <summary>
    /// Get transactional email events (opens, clicks, deliveries, etc.)
    /// </summary>
    /// <param name="startDate">Start date for the report</param>
    /// <param name="endDate">End date for the report</param>
    /// <param name="limit">Number of records to fetch (max 2500)</param>
    /// <param name="offset">Offset for pagination</param>
    /// <param name="email">Filter by specific email address</param>
    /// <param name="templateId">Filter by template ID</param>
    /// <param name="messageId">Filter by specific message ID</param>
    /// <param name="eventType">Filter by event type (sent, delivery, bounce, request, opened, click, invalid, deferred, blocked)</param>
    /// <returns>Email event report</returns>
    Task<GetEmailEventReport> GetTransactionalEmailEvents(
        DateTime? startDate = null,
        DateTime? endDate = null,
        long? limit = 2500,
        long? offset = 0,
        string email = null,
        long? templateId = null,
        string messageId = null,
        string eventType = null);

    /// <summary>
    /// Get aggregated transactional email statistics
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <param name="days">Number of days (alternative to date range)</param>
    /// <param name="tag">Filter by tag</param>
    /// <returns>Aggregated email statistics</returns>
    Task<GetAggregatedReport> GetTransactionalEmailStats(
        DateTime? startDate = null,
        DateTime? endDate = null,
        long? days = null,
        string tag = null);

    /// <summary>
    /// Get detailed statistics for emails by day
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <param name="days">Number of days</param>
    /// <param name="tag">Filter by tag</param>
    /// <returns>Daily email statistics</returns>
    Task<GetReports> GetDailyEmailStats(
        DateTime? startDate = null,
        DateTime? endDate = null,
        long? days = null,
        string tag = null);

    /// <summary>
    /// Get email events for a specific email address
    /// </summary>
    /// <param name="emailAddress">The email address to search for</param>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <param name="limit">Number of records</param>
    /// <returns>Email events for the specified address</returns>
    Task<GetEmailEventReport> GetEmailEventsForAddress(
        string emailAddress,
        DateTime? startDate = null,
        DateTime? endDate = null,
        long? limit = 100);

    /// <summary>
    /// Get click-through rate for a specific period
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <param name="tag">Optional tag filter</param>
    /// <returns>Click-through rate as decimal</returns>
    Task<decimal?> GetTransactionalClickThroughRate(
        DateTime? startDate = null,
        DateTime? endDate = null,
        string tag = null);

    /// <summary>
    /// Get comprehensive email analytics for a period
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <param name="tag">Optional tag filter</param>
    /// <returns>Comprehensive email metrics</returns>
    Task<TransactionalEmailMetricsDTO> GetTransactionalEmailMetrics(
        DateTime? startDate = null,
        DateTime? endDate = null,
        string tag = null);

    /// <summary>
    /// Get emails sent for a specific advert/tag
    /// </summary>
    /// <param name="advertTag">The tag identifying the advert</param>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <returns>Email events for the advert</returns>
    Task<GetEmailEventReport> GetEmailsForAdvert(
        string advertTag,
        DateTime? startDate = null,
        DateTime? endDate = null);

    /// <summary>
    /// Fetch email events from the past 48 hours and store new ones in the AdvertEvent table
    /// </summary>
    /// <returns>Number of new events stored</returns>
    Task<int> StoreRecentEmailEvents();

    Task<List<ContactIssueDTO>> GetContactsWithIssues(DateTime? startDate = null, DateTime? endDate = null, long? limit = 50, long? offset = 0);
    Task<List<ContactIssueDTO>> GetContactsByIssueType(string issueType, DateTime? startDate = null, DateTime? endDate = null);
    Task<ContactIssuesSummaryDTO> GetContactIssuesSummary(DateTime? startDate = null, DateTime? endDate = null);
  }
}
