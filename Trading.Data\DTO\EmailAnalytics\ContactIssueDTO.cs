﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.EmailAnalytics;
public class ContactIssueDTO
{
  public string Email { get; set; }

  public string IssueType { get; set; } // unsubscribed, blocked, hardBounce, spam, etc.
  public string IssueDescription { get; set; } 
  public DateTime IssueDate { get; set; }
  public string Sender { get; set; } // The sender that was blocked for
}
