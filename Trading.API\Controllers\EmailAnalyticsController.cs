﻿using Microsoft.AspNetCore.Mvc;
using Trading.Services.Interfaces;
using System;
using System.Threading.Tasks;

namespace Trading.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class EmailAnalyticsController : ControllerBase
{
  private readonly IEmailAnalyticsService _emailAnalyticsService;

  public EmailAnalyticsController(IEmailAnalyticsService emailAnalyticsService)
  {
    _emailAnalyticsService = emailAnalyticsService;
  }

  /// <summary>
  /// Get comprehensive email metrics for a date range
  /// </summary>
  [HttpGet("metrics")]
  public async Task<IActionResult> GetEmailMetrics(
      DateTime? startDate = null,
      DateTime? endDate = null,
      string tag = null)
  {
    var result = await _emailAnalyticsService.GetTransactionalEmailMetrics(startDate, endDate, tag);
    return Ok(result);
  }

  /// <summary>
  /// Get click-through rate for a specific period
  /// </summary>
  [HttpGet("ctr")]
  public async Task<IActionResult> GetClickThroughRate(
      DateTime? startDate = null,
      DateTime? endDate = null,
      string tag = null)
  {
    var result = await _emailAnalyticsService.GetTransactionalClickThroughRate(startDate, endDate, tag);
    return Ok(result);
  }

  /// <summary>
  /// Get email events (opens, clicks, deliveries, bounces, etc.)
  /// </summary>
  [HttpGet("events")]
  public async Task<IActionResult> GetEmailEvents(
      DateTime? startDate = null,
      DateTime? endDate = null,
      int limit = 100,
      int offset = 0,
      string email = null,
      long? templateId = null,
      string messageId = null,
      string eventType = null)
  {
    var result = await _emailAnalyticsService.GetTransactionalEmailEvents(
        startDate, endDate, limit, offset, email, templateId, messageId, eventType);
    return Ok(result);
  }

  /// <summary>
  /// Get email events for a specific email address
  /// </summary>
  [HttpGet("events/email/{emailAddress}")]
  public async Task<IActionResult> GetEventsForEmail(
      string emailAddress,
      DateTime? startDate = null,
      DateTime? endDate = null,
      int limit = 100)
  {
    var result = await _emailAnalyticsService.GetEmailEventsForAddress(emailAddress, startDate, endDate, limit);
    return Ok(result);
  }

  /// <summary>
  /// Get emails related to a specific advert (by tag)
  /// </summary>
  [HttpGet("advert/{advertTag}")]
  public async Task<IActionResult> GetAdvertEmails(
      string advertTag,
      DateTime? startDate = null,
      DateTime? endDate = null)
  {
    var result = await _emailAnalyticsService.GetEmailsForAdvert(advertTag, startDate, endDate);
    return Ok(result);
  }

  /// <summary>
  /// Get daily email statistics
  /// </summary>
  [HttpGet("daily")]
  public async Task<IActionResult> GetDailyStats(
      DateTime? startDate = null,
      DateTime? endDate = null,
      long? days = null,
      string tag = null)
  {
    var result = await _emailAnalyticsService.GetDailyEmailStats(startDate, endDate, days, tag);
    return Ok(result);
  }

  /// <summary>
  /// Get aggregated email statistics
  /// </summary>
  [HttpGet("aggregated")]
  public async Task<IActionResult> GetAggregatedStats(
      DateTime? startDate = null,
      DateTime? endDate = null,
      long? days = null,
      string tag = null)
  {
    var result = await _emailAnalyticsService.GetTransactionalEmailStats(startDate, endDate, days, tag);
    return Ok(result);
  }

  /// <summary>
  /// Store email events from the past 48 hours in the AdvertEvent table
  /// </summary>
  [HttpPost("store-events")]
  public async Task<IActionResult> StoreRecentEvents()
  {
    try
    {
      var newEventsCount = await _emailAnalyticsService.StoreRecentEmailEvents();
      return Ok(new {
        success = true,
        message = $"Successfully stored {newEventsCount} new email events",
        newEventsCount = newEventsCount
      });
    }
    catch (Exception ex)
    {
      return BadRequest(new {
        success = false,
        message = "Failed to store email events",
        error = ex.Message
      });
    }
  }

  /// <summary>
  /// Get all contacts with delivery issues (unsubscribed, blocked, spam, hard bounces)
  /// </summary>
  [HttpGet("contact-issues")]
  public async Task<IActionResult> GetContactsWithIssues(
      DateTime? startDate = null,
      DateTime? endDate = null,
      long? limit = 50,
      long? offset = 0)
  {
    var result = await _emailAnalyticsService.GetContactsWithIssues(startDate, endDate, limit, offset);
    return Ok(result);
  }

  /// <summary>
  /// Get contacts filtered by specific issue type
  /// </summary>
  [HttpGet("contact-issues/{issueType}")]
  public async Task<IActionResult> GetContactsByIssueType(
      string issueType,
      DateTime? startDate = null,
      DateTime? endDate = null)
  {
    var result = await _emailAnalyticsService.GetContactsByIssueType(issueType, startDate, endDate);
    return Ok(result);
  }

  /// <summary>
  /// Get summary statistics of all contact issues
  /// </summary>
  [HttpGet("contact-issues/summary")]
  public async Task<IActionResult> GetContactIssuesSummary(
      DateTime? startDate = null,
      DateTime? endDate = null)
  {
    var result = await _emailAnalyticsService.GetContactIssuesSummary(startDate, endDate);
    return Ok(result);
  }
}